set(CMAKE_SYSTEM_NAME Linux)
set(CMAKE_SYSTEM_PROCESSOR armhf)

set(CMAKE_C_COMPILER /home/<USER>/panpan/code/shunzao_ai_lib-develop/am3103_toolchain/bin/arm-linux-gnueabihf-gcc)
set(CMAKE_CXX_COMPILER /home/<USER>/panpan/code/shunzao_ai_lib-develop/am3103_toolchain/bin/arm-linux-gnueabihf-g++)

set(CMAKE_C_FLAGS "-march=armv7-a -mfloat-abi=hard -mfpu=neon-vfpv4")
set(CMAKE_CXX_FLAGS "-march=armv7-a -mfloat-abi=hard -mfpu=neon-vfpv4")
set(MNN_BUILD_FOR_ARM ON)
set(MNN_BUILD_FOR_ARM64 OFF)
set(MNN_BUILD_FOR_HOST OFF)
set(MNN_ARMV7 ON)
set(MNN_SEP_BUILD OFF)
set(MNN_OPENCL  ON)

set(CMAKE_BUILD_TYPE Release)


