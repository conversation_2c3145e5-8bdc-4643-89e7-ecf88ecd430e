# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

CMakeFiles/shunzao_ai_demo.dir/main.cc.o: ../include/data_type.h
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: ../include/shunzao_ai_lib.h
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: ../include/utils/utils.h
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/async.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/base.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/bufferpool.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/check.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/core.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/cv_cpu_dispatch.h
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/cv_cpu_helper.h
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/cvdef.h
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/cvstd.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/cvstd.inl.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/cvstd_wrapper.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/fast_math.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/hal/interface.h
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/hal/msa_macros.h
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/mat.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/mat.inl.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/matx.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/neon_utils.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/operations.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/optim.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/ovx.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/persistence.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/saturate.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/traits.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/types.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/utility.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/utils/instrumentation.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/utils/tls.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/version.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/core/vsx_utils.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/dnn.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/dnn/dict.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/dnn/dnn.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/dnn/dnn.inl.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/dnn/layer.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/dnn/utils/inference_engine.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/dnn/version.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/features2d.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/highgui.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/highgui/highgui.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/imgcodecs.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/imgproc.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/imgproc/segmentation.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/ml.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/ml/ml.inl.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/opencv_modules.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/photo.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/video.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/video/background_segm.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/video/tracking.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: /root/pan/shunzao_ai_lib-develop/3rdparty/opencv/opencv-4.9.0-aarch64-linux-sunxi-glibc/include/opencv4/opencv2/videoio.hpp
CMakeFiles/shunzao_ai_demo.dir/main.cc.o: ../main.cc

