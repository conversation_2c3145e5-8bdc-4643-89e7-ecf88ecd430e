#ifndef NETWORK_BASE_H
#define NETWORK_BASE_H
#include "data_type.h"
#include "npulib.h"  // SDK 提供的头文件

class NetworkBase : public NetworkItem {
public:
    NetworkBase();
    virtual ~NetworkBase();

    // 初始化网络模型
    bool init(const char* model_file_path, unsigned int network_id=0, unsigned char priority = 128);

    int load_input_set_output(void *file_data, unsigned int file_size);

    // 运行一次前向推理
    bool run_once();

    // 获取输出数据
    float** get_output_data(float **output_data);
    virtual void postprocess() {};
    // 预处理函数，根据实际需求实现
    uint8_t * preprocess(cv::Mat& img, int coreid, unsigned int *file_size);

    // 释放资源
    void delete_output_data(float** output_data);
    void release();
protected:
    // 可选：添加成员变量
    // ...
    unsigned int nn_in_width_;
    unsigned int nn_in_height_;
    unsigned int nn_in_channel_;

    int output_cnt_num = 1;
    float** output_data = nullptr;

    const float mean[3] = {0, 0, 0};
	const float scale[3] = {0.0039216, 0.0039216, 0.0039216};

    void *file_data = nullptr;
    uint32_t file_size;
    
};

#endif  // NETWORK_BASE_H
