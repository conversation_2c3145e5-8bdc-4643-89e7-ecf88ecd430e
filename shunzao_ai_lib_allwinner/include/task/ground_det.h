#ifndef GROUND_DET_H
#define GROUND_DET_H
#include <string>
#include <npulib.h>
#include "base_model.h"
#include "npulib.h"
#include "yolov8.h"
#include "data_type.h"


#define BOXES_FILTER_TH 0.3
#define ori_img_w 1280
#define ori_img_h 720
#define GROUND_REFINE_SCORE 1
#define top_k 20
#define FILTERBOX 1
#define SHOW_DETECTIONOUTPUT_LOG 0
// #define nms_threshold 0.45
#define ioa_threshold 0.5
#define threshold_float 0.3
#define rug_iou_thresh 0.1
#define rug_scales_iou_thresh 0.7
#define bin_djizhan_iou_thresh 0.7
#define cloth_iou_thresh 0.5
#define wire_iou_thresh 0.5
#define uchair_threshold 0.5
#define y_air_threshold_float 360  //todo
#define y_rug_threshold_float 680
#define bin_index 0
#define cloth_index 2
#define rug_index 3
#define shoe_index 4
#define wire_index 5
#define djizhan_index 8
#define scales_index 9

const float class_num_list[2]={
    0,
    8,
};
class shunzaoAiTask;

class GroundDet: public NetworkBase
{

public:
    GroundDet(const char* model_path);
    // GroundDet();
    ~GroundDet();
    // 初始化模型，传入模型路径
    bool setup(const std::string& model_path);
    // 加载输入数据并进行推理和后处理
    bool run_inference(cv::Mat& img, InputParam* inp);
    
    bool run_inference_with_time(cv::Mat& img, InputParam* inp);

    //加载输入数据并进行推理和后处理，并且计时
    bool run_inference(cv::Mat& , InputParam* ,shunzaoAiTask* );
        // 获取输出并处理（此处示例返回第一个输出的最大值索引）
    std::vector<BBoxFlag> get_prediction(int* size);

    // char* getResults(int* size);
    int loadconfig(std::string config_string);
private:
    int postprocess(cv::Mat& img, float** output_tensor);

    // void init();
private:
    // NetworkItem Net;
    int eval = 0;
    int debug_show_result = 1;
    /* data */
    int input_h = 640;
    int input_w = 640;
    int input_c = 3;
    float score_threshold_ = 0.3;
    float nms_threshold_ =0.45;
    std::vector<float> mean_score_={
        0.67094476,
        0.79869564,
        0.70399142,
        0.61118492,
        0.66352386,
        0.49859135,
        0.49170399,
        0.60432732,
        0.714039658,
        0.786953375,
        0.60063568,
    };
    // int heads_num = 1;
    std::vector<int> heads_list ={7,2,5,1};
    // float** gound_output_data = nullptr;
    int top_k_ = 30;
    int max_data_size_ = top_k_ * sizeof(BBoxFlag) + 12 + 4 + 1;
    std::vector<BBox> results_;
    char* res_addr_;

    std::vector<BBox> det_boxes_results;
    std::vector<BBoxFlag> det_boxes_flag_results;


};


#endif