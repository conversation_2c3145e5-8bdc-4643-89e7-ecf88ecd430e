Performing C SOURCE FILE Test CMAKE_HAVE_LIBC_PTHREAD failed with the following output:
Change Dir: /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_c910a/fast && /usr/bin/make -f CMakeFiles/cmTC_c910a.dir/build.make CMakeFiles/cmTC_c910a.dir/build
make[1]: Entering directory '/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_c910a.dir/src.c.o
/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-gcc   -std=gnu99 -O3 -DCMAKE_HAVE_LIBC_PTHREAD   -o CMakeFiles/cmTC_c910a.dir/src.c.o   -c /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/CMakeTmp/src.c
Linking C executable cmTC_c910a
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_c910a.dir/link.txt --verbose=1
/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-gcc  -std=gnu99 -O3 -DCMAKE_HAVE_LIBC_PTHREAD    CMakeFiles/cmTC_c910a.dir/src.c.o  -o cmTC_c910a 
/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/../lib/gcc/aarch64-none-linux-gnu/10.3.1/../../../../aarch64-none-linux-gnu/bin/ld: CMakeFiles/cmTC_c910a.dir/src.c.o: in function `main':
src.c:(.text.startup+0x1c): undefined reference to `pthread_create'
/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/../lib/gcc/aarch64-none-linux-gnu/10.3.1/../../../../aarch64-none-linux-gnu/bin/ld: src.c:(.text.startup+0x24): undefined reference to `pthread_detach'
/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/../lib/gcc/aarch64-none-linux-gnu/10.3.1/../../../../aarch64-none-linux-gnu/bin/ld: src.c:(.text.startup+0x30): undefined reference to `pthread_join'
collect2: error: ld returned 1 exit status
make[1]: *** [CMakeFiles/cmTC_c910a.dir/build.make:87: cmTC_c910a] Error 1
make[1]: Leaving directory '/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/CMakeTmp'
make: *** [Makefile:121: cmTC_c910a/fast] Error 2


Source file was:
#include <pthread.h>

void* test_func(void* data)
{
  return data;
}

int main(void)
{
  pthread_t thread;
  pthread_create(&thread, NULL, test_func, NULL);
  pthread_detach(thread);
  pthread_join(thread, NULL);
  pthread_atfork(NULL, NULL, NULL);
  pthread_exit(NULL);

  return 0;
}

Determining if the function pthread_create exists in the pthreads failed with the following output:
Change Dir: /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_7aaaf/fast && /usr/bin/make -f CMakeFiles/cmTC_7aaaf.dir/build.make CMakeFiles/cmTC_7aaaf.dir/build
make[1]: Entering directory '/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_7aaaf.dir/CheckFunctionExists.c.o
/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-gcc   -std=gnu99 -O3 -DCHECK_FUNCTION_EXISTS=pthread_create   -o CMakeFiles/cmTC_7aaaf.dir/CheckFunctionExists.c.o   -c /usr/share/cmake-3.16/Modules/CheckFunctionExists.c
Linking C executable cmTC_7aaaf
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_7aaaf.dir/link.txt --verbose=1
/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-gcc  -std=gnu99 -O3 -DCHECK_FUNCTION_EXISTS=pthread_create    CMakeFiles/cmTC_7aaaf.dir/CheckFunctionExists.c.o  -o cmTC_7aaaf  -lpthreads 
/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/../lib/gcc/aarch64-none-linux-gnu/10.3.1/../../../../aarch64-none-linux-gnu/bin/ld: cannot find -lpthreads
collect2: error: ld returned 1 exit status
make[1]: *** [CMakeFiles/cmTC_7aaaf.dir/build.make:87: cmTC_7aaaf] Error 1
make[1]: Leaving directory '/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/CMakeTmp'
make: *** [Makefile:121: cmTC_7aaaf/fast] Error 2



