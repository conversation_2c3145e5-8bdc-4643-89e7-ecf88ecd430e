#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

../3rd_party/flatbuffers/include/flatbuffers/base.h
assert.h
-
cstdint
-
cstddef
-
cstdlib
-
cstring
-
crtdbg.h
-
utility.h
-
utility
-
string
-
type_traits
-
vector
-
set
-
algorithm
-
iterator
-
memory
-
functional
-
stl_emulation.h
../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
string_view
-
experimental/string_view
-

../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
base.h
../3rd_party/flatbuffers/include/flatbuffers/base.h
cmath
-

../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
string
-
type_traits
-
vector
-
memory
-
limits
-
cctype
-

../include/MNN/AutoTime.hpp
stdint.h
-
stdio.h
-
MNN/MNNDefine.h
-

../include/MNN/ErrorCode.hpp

../include/MNN/HalideRuntime.h
stddef.h
-
stdint.h
-
stdbool.h
-

../include/MNN/ImageProcess.hpp
MNN/ErrorCode.hpp
-
MNN/Matrix.h
-
MNN/Tensor.hpp
-

../include/MNN/Interpreter.hpp
functional
-
map
-
memory
-
string
-
MNN/ErrorCode.hpp
-
MNN/MNNForwardType.h
-
MNN/Tensor.hpp
-

../include/MNN/MNNDefine.h
assert.h
-
stdio.h
-
TargetConditionals.h
-
hilog/log.h
-
android/log.h
-
syslog.h
-

../include/MNN/MNNForwardType.h
stdint.h
-
stddef.h
-

../include/MNN/Matrix.h
string.h
-
cstdint
-
MNN/Rect.h
-

../include/MNN/Rect.h
math.h
-
algorithm
-
utility
-
MNN/MNNDefine.h
-

../include/MNN/Tensor.hpp
vector
-
MNN/HalideRuntime.h
-
MNN/MNNDefine.h
-

../schema/current/CaffeOp_generated.h
Tensor_generated.h
../schema/current/Tensor_generated.h
Type_generated.h
../schema/current/Type_generated.h

../schema/current/ExtraInfo_generated.h
Tensor_generated.h
../schema/current/Tensor_generated.h
Type_generated.h
../schema/current/Type_generated.h

../schema/current/MNN_generated.h
CaffeOp_generated.h
../schema/current/CaffeOp_generated.h
ExtraInfo_generated.h
../schema/current/ExtraInfo_generated.h
TFQuantizeOp_generated.h
../schema/current/TFQuantizeOp_generated.h
Tensor_generated.h
../schema/current/Tensor_generated.h
TensorflowOp_generated.h
../schema/current/TensorflowOp_generated.h
Type_generated.h
../schema/current/Type_generated.h
UserDefine_generated.h
../schema/current/UserDefine_generated.h

../schema/current/TFQuantizeOp_generated.h
CaffeOp_generated.h
../schema/current/CaffeOp_generated.h
Tensor_generated.h
../schema/current/Tensor_generated.h
Type_generated.h
../schema/current/Type_generated.h

../schema/current/Tensor_generated.h
Type_generated.h
../schema/current/Type_generated.h

../schema/current/TensorflowOp_generated.h
Tensor_generated.h
../schema/current/Tensor_generated.h
Type_generated.h
../schema/current/Type_generated.h

../schema/current/Type_generated.h
flatbuffers/flatbuffers.h
../schema/current/flatbuffers/flatbuffers.h

../schema/current/UserDefine_generated.h
Tensor_generated.h
../schema/current/Tensor_generated.h
Type_generated.h
../schema/current/Type_generated.h

../source/backend/cpu/CPUBackend.hpp
map
-
memory
-
MNN/AutoTime.hpp
-
core/Backend.hpp
../source/backend/cpu/core/Backend.hpp
core/Execution.hpp
../source/backend/cpu/core/Execution.hpp
core/BufferAllocator.hpp
../source/backend/cpu/core/BufferAllocator.hpp
MNN_generated.h
../source/backend/cpu/MNN_generated.h
ThreadPool.hpp
../source/backend/cpu/ThreadPool.hpp

../source/backend/cpu/CPUImageProcess.hpp
backend/cpu/CPUBackend.hpp
../source/backend/cpu/backend/cpu/CPUBackend.hpp
MNN/ImageProcess.hpp
-
compute/CommonOptFunction.h
../source/backend/cpu/compute/CommonOptFunction.h
cv/ImageProcessUtils.hpp
../source/backend/cpu/cv/ImageProcessUtils.hpp

../source/backend/cpu/CPUTensorConvert.hpp
core/Execution.hpp
../source/backend/cpu/core/Execution.hpp
Tensor_generated.h
../source/backend/cpu/Tensor_generated.h
compute/CommonOptFunction.h
../source/backend/cpu/compute/CommonOptFunction.h

../source/backend/cpu/ThreadPool.hpp
condition_variable
-
functional
-
mutex
-
thread
-
vector
-
atomic
-
MNN/MNNDefine.h
-

../source/backend/cpu/compute/CommonOptFunction.h
stdint.h
-
stdio.h
-
string.h
-
vector
-
MNN/Rect.h
-
core/Macro.h
../source/backend/cpu/compute/core/Macro.h
backend/cpu/compute/Int8FunctionsOpt.h
../source/backend/cpu/compute/backend/cpu/compute/Int8FunctionsOpt.h

../source/backend/cpu/compute/ImageProcessFunction.hpp
MNN/ImageProcess.hpp
-
stdio.h
-
memory
-

../source/backend/cpu/compute/Int8FunctionsOpt.h
stdint.h
-
stdio.h
-
sys/types.h
-
core/Macro.h
../source/backend/cpu/compute/core/Macro.h
core/ConvolutionCommon.hpp
../source/backend/cpu/compute/core/ConvolutionCommon.hpp
BaseTsd.h
-

../source/backend/cpu/x86_x64/cpu_id.h
MNN/MNNDefine.h
-

../source/core/AutoStorage.h
stdint.h
-
string.h
-
MNNMemoryUtils.h
../source/core/MNNMemoryUtils.h

../source/core/Backend.hpp
MNN/MNNForwardType.h
-
MNN/ErrorCode.hpp
-
map
-
Command.hpp
../source/core/Command.hpp
NonCopyable.hpp
../source/core/NonCopyable.hpp
BufferAllocator.hpp
../source/core/BufferAllocator.hpp
future
-
atomic
-

../source/core/BufferAllocator.hpp
map
-
set
-
memory
-
vector
-
MNNMemoryUtils.h
../source/core/MNNMemoryUtils.h
NonCopyable.hpp
../source/core/NonCopyable.hpp
AutoStorage.h
../source/core/AutoStorage.h
MNN/Tensor.hpp
-
MNN/ErrorCode.hpp
-

../source/core/Command.hpp
MNN/Tensor.hpp
-
AutoStorage.h
../source/core/AutoStorage.h
string
-
memory
-

../source/core/ConvolutionCommon.hpp
AutoStorage.h
../source/core/AutoStorage.h
Execution.hpp
../source/core/Execution.hpp
MNN_generated.h
../source/core/MNN_generated.h

../source/core/Execution.hpp
MNN/MNNForwardType.h
-
MNN/ErrorCode.hpp
-
MNN/Tensor.hpp
-
memory
-
string
-
NonCopyable.hpp
../source/core/NonCopyable.hpp

../source/core/MNNMemoryUtils.h
stdio.h
-
core/Macro.h
../source/core/core/Macro.h

../source/core/Macro.h
MNN/MNNDefine.h
-
BaseTsd.h
-

../source/core/NonCopyable.hpp

../source/core/TensorUtils.hpp
MNN/Tensor.hpp
-
Backend.hpp
../source/core/Backend.hpp
AutoStorage.h
../source/core/AutoStorage.h
Tensor_generated.h
../source/core/Tensor_generated.h

../source/cv/ImageProcessUtils.hpp
MNN/ErrorCode.hpp
-
MNN/Matrix.h
-
MNN/Tensor.hpp
-
MNN/ImageProcess.hpp
-
backend/cpu/compute/CommonOptFunction.h
../source/cv/backend/cpu/compute/CommonOptFunction.h

../source/cv/SkNx.h
algorithm
-
limits
-
type_traits
-
cstdint
-
core/Macro.h
../source/cv/core/Macro.h
SkNx_neon.h
../source/cv/SkNx_neon.h

../source/cv/SkNx_neon.h
arm_neon.h
-

/root/pan/shunzao_ai_lib-develop/MNN-master/source/cv/ImageProcess.cpp
algorithm
-
map
-
MNN/ImageProcess.hpp
-
core/Macro.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/cv/core/Macro.h
core/TensorUtils.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/cv/core/TensorUtils.hpp
MNN/AutoTime.hpp
-
backend/cpu/CPUTensorConvert.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/cv/backend/cpu/CPUTensorConvert.hpp
MNN/MNNForwardType.h
-
core/Backend.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/cv/core/Backend.hpp
MNN/Tensor.hpp
-
MNN/Interpreter.hpp
-
core/Execution.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/cv/core/Execution.hpp
core/Backend.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/cv/core/Backend.hpp
MNN_generated.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/cv/MNN_generated.h
ImageProcessUtils.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/cv/ImageProcessUtils.hpp
backend/cpu/x86_x64/cpu_id.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/cv/backend/cpu/x86_x64/cpu_id.h

/root/pan/shunzao_ai_lib-develop/MNN-master/source/cv/ImageProcessUtils.cpp
algorithm
-
map
-
ImageProcessUtils.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/cv/ImageProcessUtils.hpp
core/Macro.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/cv/core/Macro.h
core/TensorUtils.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/cv/core/TensorUtils.hpp
backend/cpu/CPUTensorConvert.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/cv/backend/cpu/CPUTensorConvert.hpp
backend/cpu/CPUImageProcess.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/cv/backend/cpu/CPUImageProcess.hpp
backend/cpu/compute/ImageProcessFunction.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/cv/backend/cpu/compute/ImageProcessFunction.hpp
MNN/MNNForwardType.h
-
core/Backend.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/cv/core/Backend.hpp
MNN/ImageProcess.hpp
-
MNN/Tensor.hpp
-
MNN/Interpreter.hpp
-
core/Execution.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/cv/core/Execution.hpp
core/Backend.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/cv/core/Backend.hpp
MNN_generated.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/cv/MNN_generated.h
backend/cpu/x86_x64/cpu_id.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/cv/backend/cpu/x86_x64/cpu_id.h

/root/pan/shunzao_ai_lib-develop/MNN-master/source/cv/ImageProcessUtils.hpp
MNN/ErrorCode.hpp
-
MNN/Matrix.h
-
MNN/Tensor.hpp
-
MNN/ImageProcess.hpp
-
backend/cpu/compute/CommonOptFunction.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/cv/backend/cpu/compute/CommonOptFunction.h

/root/pan/shunzao_ai_lib-develop/MNN-master/source/cv/Matrix_CV.cpp
math.h
-
cstddef
-
cstdint
-
utility
-
MNN/Matrix.h
-
cv/SkNx.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/cv/cv/SkNx.h

