# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /root/pan/shunzao_ai_lib-develop/MNN-master

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm

# Include any dependencies generated for this target.
include CMakeFiles/MNNCV.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/MNNCV.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/MNNCV.dir/flags.make

CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.o: CMakeFiles/MNNCV.dir/flags.make
CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.o: ../source/cv/ImageProcess.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/cv/ImageProcess.cpp

CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/cv/ImageProcess.cpp > CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.i

CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/cv/ImageProcess.cpp -o CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.s

CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: CMakeFiles/MNNCV.dir/flags.make
CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../source/cv/ImageProcessUtils.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/cv/ImageProcessUtils.cpp

CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/cv/ImageProcessUtils.cpp > CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.i

CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/cv/ImageProcessUtils.cpp -o CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.s

CMakeFiles/MNNCV.dir/source/cv/Matrix_CV.cpp.o: CMakeFiles/MNNCV.dir/flags.make
CMakeFiles/MNNCV.dir/source/cv/Matrix_CV.cpp.o: ../source/cv/Matrix_CV.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/MNNCV.dir/source/cv/Matrix_CV.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNCV.dir/source/cv/Matrix_CV.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/cv/Matrix_CV.cpp

CMakeFiles/MNNCV.dir/source/cv/Matrix_CV.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNCV.dir/source/cv/Matrix_CV.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/cv/Matrix_CV.cpp > CMakeFiles/MNNCV.dir/source/cv/Matrix_CV.cpp.i

CMakeFiles/MNNCV.dir/source/cv/Matrix_CV.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNCV.dir/source/cv/Matrix_CV.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/cv/Matrix_CV.cpp -o CMakeFiles/MNNCV.dir/source/cv/Matrix_CV.cpp.s

MNNCV: CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.o
MNNCV: CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o
MNNCV: CMakeFiles/MNNCV.dir/source/cv/Matrix_CV.cpp.o
MNNCV: CMakeFiles/MNNCV.dir/build.make

.PHONY : MNNCV

# Rule to build all files generated by this target.
CMakeFiles/MNNCV.dir/build: MNNCV

.PHONY : CMakeFiles/MNNCV.dir/build

CMakeFiles/MNNCV.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/MNNCV.dir/cmake_clean.cmake
.PHONY : CMakeFiles/MNNCV.dir/clean

CMakeFiles/MNNCV.dir/depend:
	cd /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /root/pan/shunzao_ai_lib-develop/MNN-master /root/pan/shunzao_ai_lib-develop/MNN-master /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNCV.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/MNNCV.dir/depend

