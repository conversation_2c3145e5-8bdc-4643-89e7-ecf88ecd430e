# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.o: ../include/MNN/AutoTime.hpp
CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.o: ../include/MNN/ImageProcess.hpp
CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.o: ../include/MNN/Interpreter.hpp
CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.o: ../include/MNN/Matrix.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.o: ../include/MNN/Rect.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.o: ../source/backend/cpu/CPUTensorConvert.hpp
CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.o: ../source/backend/cpu/compute/CommonOptFunction.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.o: ../source/backend/cpu/compute/Int8FunctionsOpt.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.o: ../source/backend/cpu/x86_x64/cpu_id.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.o: ../source/core/ConvolutionCommon.hpp
CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.o: ../source/cv/ImageProcess.cpp
CMakeFiles/MNNCV.dir/source/cv/ImageProcess.cpp.o: ../source/cv/ImageProcessUtils.hpp

CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../include/MNN/AutoTime.hpp
CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../include/MNN/ImageProcess.hpp
CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../include/MNN/Interpreter.hpp
CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../include/MNN/Matrix.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../include/MNN/Rect.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../source/backend/cpu/CPUBackend.hpp
CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../source/backend/cpu/CPUImageProcess.hpp
CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../source/backend/cpu/CPUTensorConvert.hpp
CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../source/backend/cpu/ThreadPool.hpp
CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../source/backend/cpu/compute/CommonOptFunction.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../source/backend/cpu/compute/ImageProcessFunction.hpp
CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../source/backend/cpu/compute/Int8FunctionsOpt.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../source/backend/cpu/x86_x64/cpu_id.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../source/core/ConvolutionCommon.hpp
CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../source/cv/ImageProcessUtils.hpp
CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../source/cv/ImageProcessUtils.cpp
CMakeFiles/MNNCV.dir/source/cv/ImageProcessUtils.cpp.o: ../source/cv/ImageProcessUtils.hpp

CMakeFiles/MNNCV.dir/source/cv/Matrix_CV.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNCV.dir/source/cv/Matrix_CV.cpp.o: ../include/MNN/Matrix.h
CMakeFiles/MNNCV.dir/source/cv/Matrix_CV.cpp.o: ../include/MNN/Rect.h
CMakeFiles/MNNCV.dir/source/cv/Matrix_CV.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNCV.dir/source/cv/Matrix_CV.cpp.o: ../source/cv/SkNx.h
CMakeFiles/MNNCV.dir/source/cv/Matrix_CV.cpp.o: ../source/cv/SkNx_neon.h
CMakeFiles/MNNCV.dir/source/cv/Matrix_CV.cpp.o: ../source/cv/Matrix_CV.cpp

