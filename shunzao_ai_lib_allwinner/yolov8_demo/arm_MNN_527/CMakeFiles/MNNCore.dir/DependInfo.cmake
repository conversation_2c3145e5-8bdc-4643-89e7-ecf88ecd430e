# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "CXX"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_CXX
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/core/AutoTime.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNCore.dir/source/core/AutoTime.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/core/Backend.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNCore.dir/source/core/Backend.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/core/BufferAllocator.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNCore.dir/source/core/BufferAllocator.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/core/ConvolutionCommon.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/core/Execution.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNCore.dir/source/core/Execution.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/core/FileLoader.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNCore.dir/source/core/FileLoader.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/core/Interpreter.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/core/MNNFileUtils.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNCore.dir/source/core/MNNFileUtils.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/core/MNNMemoryUtils.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNCore.dir/source/core/MNNMemoryUtils.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/core/OpCommonUtils.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/core/Pipeline.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/core/RuntimeFactory.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNCore.dir/source/core/RuntimeFactory.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/core/Schedule.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/core/Session.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNCore.dir/source/core/Session.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/core/Tensor.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNCore.dir/source/core/Tensor.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/core/TensorUtils.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNCore.dir/source/core/TensorUtils.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/core/WorkerThread.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNCore.dir/source/core/WorkerThread.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/core/WrapExecution.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o"
  )
set(CMAKE_CXX_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_CXX
  "MNN_KLEIDIAI_ENABLED=1"
  "MNN_SME2"
  "MNN_SUPPORT_DEPRECATED_OPV2"
  "MNN_SUPPORT_QUANT_EXTEND"
  "MNN_USE_NEON"
  "MNN_USE_THREAD_POOL"
  )

# The include file search paths:
set(CMAKE_CXX_TARGET_INCLUDE_PATH
  "../include"
  "../source"
  "../express"
  "../tools"
  "../codegen"
  "../schema/current"
  "../3rd_party"
  "../3rd_party/flatbuffers/include"
  "../3rd_party/half"
  "../3rd_party/imageHelper"
  "../3rd_party/OpenCLHeaders"
  "_deps/kleidiai-v1.9.0"
  "_deps/kleidiai-v1.9.0/kai"
  "_deps/kleidiai-v1.9.0/kai/ukernels"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/matmul_clamp_f32_qai8dxp_qsi4cxp"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/matmul_clamp_f16_qsi8d32p_qai4c32p"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/matmul_clamp_f32_qsi8d32p_qai4c32p"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/pack"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/matmul_clamp_f32_f32p_f32p"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/matmul_clamp_f32_f32_f32p"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/matmul_clamp_f16_f16p_f16p"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/matmul_clamp_f16_f16_f16p"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/imatmul_clamp_f32_f32p_f32p"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/imatmul_clamp_f16_f16p_f16p"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
