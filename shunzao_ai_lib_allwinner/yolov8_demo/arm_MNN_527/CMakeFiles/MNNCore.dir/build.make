# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /root/pan/shunzao_ai_lib-develop/MNN-master

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm

# Include any dependencies generated for this target.
include CMakeFiles/MNNCore.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/MNNCore.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/MNNCore.dir/flags.make

CMakeFiles/MNNCore.dir/source/core/AutoTime.cpp.o: CMakeFiles/MNNCore.dir/flags.make
CMakeFiles/MNNCore.dir/source/core/AutoTime.cpp.o: ../source/core/AutoTime.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/MNNCore.dir/source/core/AutoTime.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNCore.dir/source/core/AutoTime.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/AutoTime.cpp

CMakeFiles/MNNCore.dir/source/core/AutoTime.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNCore.dir/source/core/AutoTime.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/AutoTime.cpp > CMakeFiles/MNNCore.dir/source/core/AutoTime.cpp.i

CMakeFiles/MNNCore.dir/source/core/AutoTime.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNCore.dir/source/core/AutoTime.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/AutoTime.cpp -o CMakeFiles/MNNCore.dir/source/core/AutoTime.cpp.s

CMakeFiles/MNNCore.dir/source/core/Backend.cpp.o: CMakeFiles/MNNCore.dir/flags.make
CMakeFiles/MNNCore.dir/source/core/Backend.cpp.o: ../source/core/Backend.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/MNNCore.dir/source/core/Backend.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNCore.dir/source/core/Backend.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/Backend.cpp

CMakeFiles/MNNCore.dir/source/core/Backend.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNCore.dir/source/core/Backend.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/Backend.cpp > CMakeFiles/MNNCore.dir/source/core/Backend.cpp.i

CMakeFiles/MNNCore.dir/source/core/Backend.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNCore.dir/source/core/Backend.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/Backend.cpp -o CMakeFiles/MNNCore.dir/source/core/Backend.cpp.s

CMakeFiles/MNNCore.dir/source/core/BufferAllocator.cpp.o: CMakeFiles/MNNCore.dir/flags.make
CMakeFiles/MNNCore.dir/source/core/BufferAllocator.cpp.o: ../source/core/BufferAllocator.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/MNNCore.dir/source/core/BufferAllocator.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNCore.dir/source/core/BufferAllocator.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/BufferAllocator.cpp

CMakeFiles/MNNCore.dir/source/core/BufferAllocator.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNCore.dir/source/core/BufferAllocator.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/BufferAllocator.cpp > CMakeFiles/MNNCore.dir/source/core/BufferAllocator.cpp.i

CMakeFiles/MNNCore.dir/source/core/BufferAllocator.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNCore.dir/source/core/BufferAllocator.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/BufferAllocator.cpp -o CMakeFiles/MNNCore.dir/source/core/BufferAllocator.cpp.s

CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o: CMakeFiles/MNNCore.dir/flags.make
CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o: ../source/core/ConvolutionCommon.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/ConvolutionCommon.cpp

CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/ConvolutionCommon.cpp > CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.i

CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/ConvolutionCommon.cpp -o CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.s

CMakeFiles/MNNCore.dir/source/core/Execution.cpp.o: CMakeFiles/MNNCore.dir/flags.make
CMakeFiles/MNNCore.dir/source/core/Execution.cpp.o: ../source/core/Execution.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/MNNCore.dir/source/core/Execution.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNCore.dir/source/core/Execution.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/Execution.cpp

CMakeFiles/MNNCore.dir/source/core/Execution.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNCore.dir/source/core/Execution.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/Execution.cpp > CMakeFiles/MNNCore.dir/source/core/Execution.cpp.i

CMakeFiles/MNNCore.dir/source/core/Execution.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNCore.dir/source/core/Execution.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/Execution.cpp -o CMakeFiles/MNNCore.dir/source/core/Execution.cpp.s

CMakeFiles/MNNCore.dir/source/core/FileLoader.cpp.o: CMakeFiles/MNNCore.dir/flags.make
CMakeFiles/MNNCore.dir/source/core/FileLoader.cpp.o: ../source/core/FileLoader.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/MNNCore.dir/source/core/FileLoader.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNCore.dir/source/core/FileLoader.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/FileLoader.cpp

CMakeFiles/MNNCore.dir/source/core/FileLoader.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNCore.dir/source/core/FileLoader.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/FileLoader.cpp > CMakeFiles/MNNCore.dir/source/core/FileLoader.cpp.i

CMakeFiles/MNNCore.dir/source/core/FileLoader.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNCore.dir/source/core/FileLoader.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/FileLoader.cpp -o CMakeFiles/MNNCore.dir/source/core/FileLoader.cpp.s

CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o: CMakeFiles/MNNCore.dir/flags.make
CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o: ../source/core/Interpreter.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/Interpreter.cpp

CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/Interpreter.cpp > CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.i

CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/Interpreter.cpp -o CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.s

CMakeFiles/MNNCore.dir/source/core/MNNFileUtils.cpp.o: CMakeFiles/MNNCore.dir/flags.make
CMakeFiles/MNNCore.dir/source/core/MNNFileUtils.cpp.o: ../source/core/MNNFileUtils.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object CMakeFiles/MNNCore.dir/source/core/MNNFileUtils.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNCore.dir/source/core/MNNFileUtils.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/MNNFileUtils.cpp

CMakeFiles/MNNCore.dir/source/core/MNNFileUtils.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNCore.dir/source/core/MNNFileUtils.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/MNNFileUtils.cpp > CMakeFiles/MNNCore.dir/source/core/MNNFileUtils.cpp.i

CMakeFiles/MNNCore.dir/source/core/MNNFileUtils.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNCore.dir/source/core/MNNFileUtils.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/MNNFileUtils.cpp -o CMakeFiles/MNNCore.dir/source/core/MNNFileUtils.cpp.s

CMakeFiles/MNNCore.dir/source/core/MNNMemoryUtils.cpp.o: CMakeFiles/MNNCore.dir/flags.make
CMakeFiles/MNNCore.dir/source/core/MNNMemoryUtils.cpp.o: ../source/core/MNNMemoryUtils.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object CMakeFiles/MNNCore.dir/source/core/MNNMemoryUtils.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNCore.dir/source/core/MNNMemoryUtils.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/MNNMemoryUtils.cpp

CMakeFiles/MNNCore.dir/source/core/MNNMemoryUtils.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNCore.dir/source/core/MNNMemoryUtils.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/MNNMemoryUtils.cpp > CMakeFiles/MNNCore.dir/source/core/MNNMemoryUtils.cpp.i

CMakeFiles/MNNCore.dir/source/core/MNNMemoryUtils.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNCore.dir/source/core/MNNMemoryUtils.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/MNNMemoryUtils.cpp -o CMakeFiles/MNNCore.dir/source/core/MNNMemoryUtils.cpp.s

CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.o: CMakeFiles/MNNCore.dir/flags.make
CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.o: ../source/core/OpCommonUtils.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/OpCommonUtils.cpp

CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/OpCommonUtils.cpp > CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.i

CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/OpCommonUtils.cpp -o CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.s

CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o: CMakeFiles/MNNCore.dir/flags.make
CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o: ../source/core/Pipeline.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/Pipeline.cpp

CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/Pipeline.cpp > CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.i

CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/Pipeline.cpp -o CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.s

CMakeFiles/MNNCore.dir/source/core/RuntimeFactory.cpp.o: CMakeFiles/MNNCore.dir/flags.make
CMakeFiles/MNNCore.dir/source/core/RuntimeFactory.cpp.o: ../source/core/RuntimeFactory.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building CXX object CMakeFiles/MNNCore.dir/source/core/RuntimeFactory.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNCore.dir/source/core/RuntimeFactory.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/RuntimeFactory.cpp

CMakeFiles/MNNCore.dir/source/core/RuntimeFactory.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNCore.dir/source/core/RuntimeFactory.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/RuntimeFactory.cpp > CMakeFiles/MNNCore.dir/source/core/RuntimeFactory.cpp.i

CMakeFiles/MNNCore.dir/source/core/RuntimeFactory.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNCore.dir/source/core/RuntimeFactory.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/RuntimeFactory.cpp -o CMakeFiles/MNNCore.dir/source/core/RuntimeFactory.cpp.s

CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.o: CMakeFiles/MNNCore.dir/flags.make
CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.o: ../source/core/Schedule.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building CXX object CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/Schedule.cpp

CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/Schedule.cpp > CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.i

CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/Schedule.cpp -o CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.s

CMakeFiles/MNNCore.dir/source/core/Session.cpp.o: CMakeFiles/MNNCore.dir/flags.make
CMakeFiles/MNNCore.dir/source/core/Session.cpp.o: ../source/core/Session.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building CXX object CMakeFiles/MNNCore.dir/source/core/Session.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNCore.dir/source/core/Session.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/Session.cpp

CMakeFiles/MNNCore.dir/source/core/Session.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNCore.dir/source/core/Session.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/Session.cpp > CMakeFiles/MNNCore.dir/source/core/Session.cpp.i

CMakeFiles/MNNCore.dir/source/core/Session.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNCore.dir/source/core/Session.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/Session.cpp -o CMakeFiles/MNNCore.dir/source/core/Session.cpp.s

CMakeFiles/MNNCore.dir/source/core/Tensor.cpp.o: CMakeFiles/MNNCore.dir/flags.make
CMakeFiles/MNNCore.dir/source/core/Tensor.cpp.o: ../source/core/Tensor.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building CXX object CMakeFiles/MNNCore.dir/source/core/Tensor.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNCore.dir/source/core/Tensor.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/Tensor.cpp

CMakeFiles/MNNCore.dir/source/core/Tensor.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNCore.dir/source/core/Tensor.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/Tensor.cpp > CMakeFiles/MNNCore.dir/source/core/Tensor.cpp.i

CMakeFiles/MNNCore.dir/source/core/Tensor.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNCore.dir/source/core/Tensor.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/Tensor.cpp -o CMakeFiles/MNNCore.dir/source/core/Tensor.cpp.s

CMakeFiles/MNNCore.dir/source/core/TensorUtils.cpp.o: CMakeFiles/MNNCore.dir/flags.make
CMakeFiles/MNNCore.dir/source/core/TensorUtils.cpp.o: ../source/core/TensorUtils.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building CXX object CMakeFiles/MNNCore.dir/source/core/TensorUtils.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNCore.dir/source/core/TensorUtils.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/TensorUtils.cpp

CMakeFiles/MNNCore.dir/source/core/TensorUtils.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNCore.dir/source/core/TensorUtils.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/TensorUtils.cpp > CMakeFiles/MNNCore.dir/source/core/TensorUtils.cpp.i

CMakeFiles/MNNCore.dir/source/core/TensorUtils.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNCore.dir/source/core/TensorUtils.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/TensorUtils.cpp -o CMakeFiles/MNNCore.dir/source/core/TensorUtils.cpp.s

CMakeFiles/MNNCore.dir/source/core/WorkerThread.cpp.o: CMakeFiles/MNNCore.dir/flags.make
CMakeFiles/MNNCore.dir/source/core/WorkerThread.cpp.o: ../source/core/WorkerThread.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building CXX object CMakeFiles/MNNCore.dir/source/core/WorkerThread.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNCore.dir/source/core/WorkerThread.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/WorkerThread.cpp

CMakeFiles/MNNCore.dir/source/core/WorkerThread.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNCore.dir/source/core/WorkerThread.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/WorkerThread.cpp > CMakeFiles/MNNCore.dir/source/core/WorkerThread.cpp.i

CMakeFiles/MNNCore.dir/source/core/WorkerThread.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNCore.dir/source/core/WorkerThread.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/WorkerThread.cpp -o CMakeFiles/MNNCore.dir/source/core/WorkerThread.cpp.s

CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o: CMakeFiles/MNNCore.dir/flags.make
CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o: ../source/core/WrapExecution.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building CXX object CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/WrapExecution.cpp

CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/WrapExecution.cpp > CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.i

CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/WrapExecution.cpp -o CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.s

MNNCore: CMakeFiles/MNNCore.dir/source/core/AutoTime.cpp.o
MNNCore: CMakeFiles/MNNCore.dir/source/core/Backend.cpp.o
MNNCore: CMakeFiles/MNNCore.dir/source/core/BufferAllocator.cpp.o
MNNCore: CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o
MNNCore: CMakeFiles/MNNCore.dir/source/core/Execution.cpp.o
MNNCore: CMakeFiles/MNNCore.dir/source/core/FileLoader.cpp.o
MNNCore: CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o
MNNCore: CMakeFiles/MNNCore.dir/source/core/MNNFileUtils.cpp.o
MNNCore: CMakeFiles/MNNCore.dir/source/core/MNNMemoryUtils.cpp.o
MNNCore: CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.o
MNNCore: CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o
MNNCore: CMakeFiles/MNNCore.dir/source/core/RuntimeFactory.cpp.o
MNNCore: CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.o
MNNCore: CMakeFiles/MNNCore.dir/source/core/Session.cpp.o
MNNCore: CMakeFiles/MNNCore.dir/source/core/Tensor.cpp.o
MNNCore: CMakeFiles/MNNCore.dir/source/core/TensorUtils.cpp.o
MNNCore: CMakeFiles/MNNCore.dir/source/core/WorkerThread.cpp.o
MNNCore: CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o
MNNCore: CMakeFiles/MNNCore.dir/build.make

.PHONY : MNNCore

# Rule to build all files generated by this target.
CMakeFiles/MNNCore.dir/build: MNNCore

.PHONY : CMakeFiles/MNNCore.dir/build

CMakeFiles/MNNCore.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/MNNCore.dir/cmake_clean.cmake
.PHONY : CMakeFiles/MNNCore.dir/clean

CMakeFiles/MNNCore.dir/depend:
	cd /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /root/pan/shunzao_ai_lib-develop/MNN-master /root/pan/shunzao_ai_lib-develop/MNN-master /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNCore.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/MNNCore.dir/depend

