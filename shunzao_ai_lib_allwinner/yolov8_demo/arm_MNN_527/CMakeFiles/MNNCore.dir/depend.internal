# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

CMakeFiles/MNNCore.dir/source/core/AutoTime.cpp.o
 ../include/MNN/AutoTime.hpp
 ../include/MNN/MNNDefine.h
 ../source/core/Macro.h
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/AutoTime.cpp
CMakeFiles/MNNCore.dir/source/core/Backend.cpp.o
 ../3rd_party/flatbuffers/include/flatbuffers/base.h
 ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
 ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
 ../include/MNN/ErrorCode.hpp
 ../include/MNN/HalideRuntime.h
 ../include/MNN/MNNDefine.h
 ../include/MNN/MNNForwardType.h
 ../include/MNN/Rect.h
 ../include/MNN/Tensor.hpp
 ../schema/current/CaffeOp_generated.h
 ../schema/current/ExtraInfo_generated.h
 ../schema/current/MNN_generated.h
 ../schema/current/TFQuantizeOp_generated.h
 ../schema/current/Tensor_generated.h
 ../schema/current/TensorflowOp_generated.h
 ../schema/current/Type_generated.h
 ../schema/current/UserDefine_generated.h
 ../source/backend/cpu/CPUTensorConvert.hpp
 ../source/backend/cpu/compute/CommonOptFunction.h
 ../source/backend/cpu/compute/Int8FunctionsOpt.h
 ../source/core/AutoStorage.h
 ../source/core/Backend.hpp
 ../source/core/BufferAllocator.hpp
 ../source/core/Command.hpp
 ../source/core/ConvolutionCommon.hpp
 ../source/core/Execution.hpp
 ../source/core/MNNMemoryUtils.h
 ../source/core/Macro.h
 ../source/core/NonCopyable.hpp
 ../source/core/TensorUtils.hpp
 ../source/geometry/GeometryComputer.hpp
 ../source/shape/SizeComputer.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/Backend.cpp
CMakeFiles/MNNCore.dir/source/core/BufferAllocator.cpp.o
 ../include/MNN/ErrorCode.hpp
 ../include/MNN/HalideRuntime.h
 ../include/MNN/MNNDefine.h
 ../include/MNN/Tensor.hpp
 ../source/core/AutoStorage.h
 ../source/core/BufferAllocator.hpp
 ../source/core/MNNMemoryUtils.h
 ../source/core/Macro.h
 ../source/core/NonCopyable.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/BufferAllocator.cpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/MNNFileUtils.h
CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o
 ../3rd_party/flatbuffers/include/flatbuffers/base.h
 ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
 ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
 ../3rd_party/half/half.hpp
 ../include/MNN/AutoTime.hpp
 ../include/MNN/ErrorCode.hpp
 ../include/MNN/HalideRuntime.h
 ../include/MNN/MNNDefine.h
 ../include/MNN/MNNForwardType.h
 ../include/MNN/Rect.h
 ../include/MNN/Tensor.hpp
 ../schema/current/CaffeOp_generated.h
 ../schema/current/ExtraInfo_generated.h
 ../schema/current/MNN_generated.h
 ../schema/current/TFQuantizeOp_generated.h
 ../schema/current/Tensor_generated.h
 ../schema/current/TensorflowOp_generated.h
 ../schema/current/Type_generated.h
 ../schema/current/UserDefine_generated.h
 ../source/backend/cpu/CPUBackend.hpp
 ../source/backend/cpu/ThreadPool.hpp
 ../source/backend/cpu/compute/CommonOptFunction.h
 ../source/backend/cpu/compute/Int8FunctionsOpt.h
 ../source/core/AutoStorage.h
 ../source/core/Backend.hpp
 ../source/core/BufferAllocator.hpp
 ../source/core/Command.hpp
 ../source/core/ConvolutionCommon.hpp
 ../source/core/Execution.hpp
 ../source/core/FileLoader.hpp
 ../source/core/Macro.h
 ../source/core/OpCommonUtils.hpp
 ../source/core/TensorUtils.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/AutoStorage.h
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/ConvolutionCommon.cpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/ConvolutionCommon.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/Execution.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/MNNFileUtils.h
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/MNNMemoryUtils.h
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/NonCopyable.hpp
CMakeFiles/MNNCore.dir/source/core/Execution.cpp.o
 ../include/MNN/ErrorCode.hpp
 ../include/MNN/HalideRuntime.h
 ../include/MNN/MNNDefine.h
 ../include/MNN/MNNForwardType.h
 ../include/MNN/Tensor.hpp
 ../source/core/Execution.hpp
 ../source/core/NonCopyable.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/Execution.cpp
CMakeFiles/MNNCore.dir/source/core/FileLoader.cpp.o
 ../include/MNN/MNNDefine.h
 ../source/core/AutoStorage.h
 ../source/core/FileLoader.hpp
 ../source/core/MNNMemoryUtils.h
 ../source/core/Macro.h
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/FileLoader.cpp
CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o
 ../3rd_party/flatbuffers/include/flatbuffers/base.h
 ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
 ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
 ../include/MNN/AutoTime.hpp
 ../include/MNN/ErrorCode.hpp
 ../include/MNN/HalideRuntime.h
 ../include/MNN/Interpreter.hpp
 ../include/MNN/MNNDefine.h
 ../include/MNN/MNNForwardType.h
 ../include/MNN/Tensor.hpp
 ../schema/current/CaffeOp_generated.h
 ../schema/current/ExtraInfo_generated.h
 ../schema/current/MNN_generated.h
 ../schema/current/TFQuantizeOp_generated.h
 ../schema/current/Tensor_generated.h
 ../schema/current/TensorflowOp_generated.h
 ../schema/current/Type_generated.h
 ../schema/current/UserDefine_generated.h
 ../source/core/AutoStorage.h
 ../source/core/Backend.hpp
 ../source/core/BufferAllocator.hpp
 ../source/core/Command.hpp
 ../source/core/Execution.hpp
 ../source/core/FileLoader.hpp
 ../source/core/MNNMemoryUtils.h
 ../source/core/Macro.h
 ../source/core/NonCopyable.hpp
 ../source/core/OpCommonUtils.hpp
 ../source/core/Pipeline.hpp
 ../source/core/RuntimeFactory.hpp
 ../source/core/Schedule.hpp
 ../source/core/Session.hpp
 ../source/core/TensorUtils.hpp
 ../source/geometry/GeometryComputer.hpp
 ../source/shape/SizeComputer.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/Interpreter.cpp
CMakeFiles/MNNCore.dir/source/core/MNNFileUtils.cpp.o
 ../include/MNN/ErrorCode.hpp
 ../include/MNN/MNNDefine.h
 ../source/core/Macro.h
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/MNNFileUtils.cpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/MNNFileUtils.h
CMakeFiles/MNNCore.dir/source/core/MNNMemoryUtils.cpp.o
 ../include/MNN/MNNDefine.h
 ../source/core/MNNMemoryUtils.h
 ../source/core/Macro.h
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/MNNMemoryUtils.cpp
CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.o
 ../3rd_party/flatbuffers/include/flatbuffers/base.h
 ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
 ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
 ../include/MNN/ErrorCode.hpp
 ../include/MNN/HalideRuntime.h
 ../include/MNN/MNNDefine.h
 ../include/MNN/MNNForwardType.h
 ../include/MNN/Tensor.hpp
 ../schema/current/CaffeOp_generated.h
 ../schema/current/ExtraInfo_generated.h
 ../schema/current/MNN_generated.h
 ../schema/current/TFQuantizeOp_generated.h
 ../schema/current/Tensor_generated.h
 ../schema/current/TensorflowOp_generated.h
 ../schema/current/Type_generated.h
 ../schema/current/UserDefine_generated.h
 ../source/core/AutoStorage.h
 ../source/core/Execution.hpp
 ../source/core/Macro.h
 ../source/core/NonCopyable.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/AutoStorage.h
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/Backend.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/BufferAllocator.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/Command.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/FileLoader.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/MNNMemoryUtils.h
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/Macro.h
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/OpCommonUtils.cpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/OpCommonUtils.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/TensorUtils.hpp
CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o
 ../3rd_party/flatbuffers/include/flatbuffers/base.h
 ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
 ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
 ../include/MNN/AutoTime.hpp
 ../include/MNN/ErrorCode.hpp
 ../include/MNN/HalideRuntime.h
 ../include/MNN/Interpreter.hpp
 ../include/MNN/MNNDefine.h
 ../include/MNN/MNNForwardType.h
 ../include/MNN/Tensor.hpp
 ../schema/current/CaffeOp_generated.h
 ../schema/current/ExtraInfo_generated.h
 ../schema/current/MNN_generated.h
 ../schema/current/TFQuantizeOp_generated.h
 ../schema/current/Tensor_generated.h
 ../schema/current/TensorflowOp_generated.h
 ../schema/current/Type_generated.h
 ../schema/current/UserDefine_generated.h
 ../source/backend/cpu/CPUBackend.hpp
 ../source/backend/cpu/ThreadPool.hpp
 ../source/backend/cpu/compute/Int8FunctionsOpt.h
 ../source/core/AutoStorage.h
 ../source/core/Backend.hpp
 ../source/core/BufferAllocator.hpp
 ../source/core/Command.hpp
 ../source/core/ConvolutionCommon.hpp
 ../source/core/Execution.hpp
 ../source/core/FileLoader.hpp
 ../source/core/MNNMemoryUtils.h
 ../source/core/Macro.h
 ../source/core/NonCopyable.hpp
 ../source/core/OpCommonUtils.hpp
 ../source/core/Pipeline.hpp
 ../source/core/Schedule.hpp
 ../source/core/TensorUtils.hpp
 ../source/core/WrapExecution.hpp
 ../source/geometry/GeometryComputer.hpp
 ../source/geometry/GeometryComputerUtils.hpp
 ../source/shape/SizeComputer.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/Pipeline.cpp
CMakeFiles/MNNCore.dir/source/core/RuntimeFactory.cpp.o
 ../include/MNN/ErrorCode.hpp
 ../include/MNN/HalideRuntime.h
 ../include/MNN/MNNDefine.h
 ../include/MNN/MNNForwardType.h
 ../include/MNN/Tensor.hpp
 ../source/core/AutoStorage.h
 ../source/core/Backend.hpp
 ../source/core/BufferAllocator.hpp
 ../source/core/Command.hpp
 ../source/core/MNNMemoryUtils.h
 ../source/core/Macro.h
 ../source/core/NonCopyable.hpp
 ../source/core/RuntimeFactory.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/RuntimeFactory.cpp
CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.o
 ../3rd_party/flatbuffers/include/flatbuffers/base.h
 ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
 ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
 ../include/MNN/AutoTime.hpp
 ../include/MNN/ErrorCode.hpp
 ../include/MNN/HalideRuntime.h
 ../include/MNN/Interpreter.hpp
 ../include/MNN/MNNDefine.h
 ../include/MNN/MNNForwardType.h
 ../include/MNN/Tensor.hpp
 ../schema/current/CaffeOp_generated.h
 ../schema/current/ExtraInfo_generated.h
 ../schema/current/MNN_generated.h
 ../schema/current/TFQuantizeOp_generated.h
 ../schema/current/Tensor_generated.h
 ../schema/current/TensorflowOp_generated.h
 ../schema/current/Type_generated.h
 ../schema/current/UserDefine_generated.h
 ../source/core/AutoStorage.h
 ../source/core/Backend.hpp
 ../source/core/BufferAllocator.hpp
 ../source/core/Command.hpp
 ../source/core/Execution.hpp
 ../source/core/FileLoader.hpp
 ../source/core/MNNMemoryUtils.h
 ../source/core/Macro.h
 ../source/core/NonCopyable.hpp
 ../source/core/RuntimeFactory.hpp
 ../source/core/Schedule.hpp
 ../source/core/TensorUtils.hpp
 ../source/geometry/GeometryComputer.hpp
 ../source/geometry/GeometryComputerUtils.hpp
 ../source/shape/SizeComputer.hpp
 ../source/utils/InitNet.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/Schedule.cpp
CMakeFiles/MNNCore.dir/source/core/Session.cpp.o
 ../3rd_party/flatbuffers/include/flatbuffers/base.h
 ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
 ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
 ../include/MNN/AutoTime.hpp
 ../include/MNN/ErrorCode.hpp
 ../include/MNN/HalideRuntime.h
 ../include/MNN/Interpreter.hpp
 ../include/MNN/MNNDefine.h
 ../include/MNN/MNNForwardType.h
 ../include/MNN/Tensor.hpp
 ../schema/current/CaffeOp_generated.h
 ../schema/current/ExtraInfo_generated.h
 ../schema/current/MNN_generated.h
 ../schema/current/TFQuantizeOp_generated.h
 ../schema/current/Tensor_generated.h
 ../schema/current/TensorflowOp_generated.h
 ../schema/current/Type_generated.h
 ../schema/current/UserDefine_generated.h
 ../source/backend/cpu/CPUBackend.hpp
 ../source/backend/cpu/ThreadPool.hpp
 ../source/backend/cpu/compute/Int8FunctionsOpt.h
 ../source/core/AutoStorage.h
 ../source/core/Backend.hpp
 ../source/core/BufferAllocator.hpp
 ../source/core/Command.hpp
 ../source/core/ConvolutionCommon.hpp
 ../source/core/Execution.hpp
 ../source/core/MNNMemoryUtils.h
 ../source/core/Macro.h
 ../source/core/NonCopyable.hpp
 ../source/core/Pipeline.hpp
 ../source/core/RuntimeFactory.hpp
 ../source/core/Schedule.hpp
 ../source/core/Session.hpp
 ../source/core/TensorUtils.hpp
 ../source/core/WrapExecution.hpp
 ../source/geometry/GeometryComputer.hpp
 ../source/shape/SizeComputer.hpp
 ../source/utils/InitNet.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/Session.cpp
CMakeFiles/MNNCore.dir/source/core/Tensor.cpp.o
 ../3rd_party/flatbuffers/include/flatbuffers/base.h
 ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
 ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
 ../include/MNN/ErrorCode.hpp
 ../include/MNN/HalideRuntime.h
 ../include/MNN/MNNDefine.h
 ../include/MNN/MNNForwardType.h
 ../include/MNN/Tensor.hpp
 ../schema/current/Tensor_generated.h
 ../schema/current/Type_generated.h
 ../source/core/AutoStorage.h
 ../source/core/Backend.hpp
 ../source/core/BufferAllocator.hpp
 ../source/core/Command.hpp
 ../source/core/MNNMemoryUtils.h
 ../source/core/Macro.h
 ../source/core/NonCopyable.hpp
 ../source/core/TensorUtils.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/Tensor.cpp
CMakeFiles/MNNCore.dir/source/core/TensorUtils.cpp.o
 ../3rd_party/flatbuffers/include/flatbuffers/base.h
 ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
 ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
 ../include/MNN/ErrorCode.hpp
 ../include/MNN/HalideRuntime.h
 ../include/MNN/MNNDefine.h
 ../include/MNN/MNNForwardType.h
 ../include/MNN/Tensor.hpp
 ../schema/current/Tensor_generated.h
 ../schema/current/Type_generated.h
 ../source/core/AutoStorage.h
 ../source/core/Backend.hpp
 ../source/core/BufferAllocator.hpp
 ../source/core/Command.hpp
 ../source/core/MNNMemoryUtils.h
 ../source/core/Macro.h
 ../source/core/NonCopyable.hpp
 ../source/core/TensorUtils.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/TensorUtils.cpp
CMakeFiles/MNNCore.dir/source/core/WorkerThread.cpp.o
 ../include/MNN/AutoTime.hpp
 ../include/MNN/MNNDefine.h
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/WorkerThread.cpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/WorkerThread.hpp
CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o
 ../3rd_party/flatbuffers/include/flatbuffers/base.h
 ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
 ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
 ../include/MNN/AutoTime.hpp
 ../include/MNN/ErrorCode.hpp
 ../include/MNN/HalideRuntime.h
 ../include/MNN/MNNDefine.h
 ../include/MNN/MNNForwardType.h
 ../include/MNN/Rect.h
 ../include/MNN/Tensor.hpp
 ../schema/current/CaffeOp_generated.h
 ../schema/current/ExtraInfo_generated.h
 ../schema/current/MNN_generated.h
 ../schema/current/TFQuantizeOp_generated.h
 ../schema/current/Tensor_generated.h
 ../schema/current/TensorflowOp_generated.h
 ../schema/current/Type_generated.h
 ../schema/current/UserDefine_generated.h
 ../source/backend/cpu/CPUBackend.hpp
 ../source/backend/cpu/CPUCast.hpp
 ../source/backend/cpu/ThreadPool.hpp
 ../source/backend/cpu/compute/CommonOptFunction.h
 ../source/backend/cpu/compute/Int8FunctionsOpt.h
 ../source/core/AutoStorage.h
 ../source/core/Backend.hpp
 ../source/core/BufferAllocator.hpp
 ../source/core/Command.hpp
 ../source/core/Concurrency.h
 ../source/core/ConvolutionCommon.hpp
 ../source/core/Execution.hpp
 ../source/core/FileLoader.hpp
 ../source/core/MNNMemoryUtils.h
 ../source/core/Macro.h
 ../source/core/NonCopyable.hpp
 ../source/core/OpCommonUtils.hpp
 ../source/core/TensorUtils.hpp
 ../source/core/WrapExecution.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/core/WrapExecution.cpp
