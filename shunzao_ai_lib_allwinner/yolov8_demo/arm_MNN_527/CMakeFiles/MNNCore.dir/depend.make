# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

CMakeFiles/MNNCore.dir/source/core/AutoTime.cpp.o: ../include/MNN/AutoTime.hpp
CMakeFiles/MNNCore.dir/source/core/AutoTime.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNCore.dir/source/core/AutoTime.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNCore.dir/source/core/AutoTime.cpp.o: ../source/core/AutoTime.cpp

CMakeFiles/MNNCore.dir/source/core/Backend.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNCore.dir/source/core/Backend.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNCore.dir/source/core/Backend.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNCore.dir/source/core/Backend.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNCore.dir/source/core/Backend.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNCore.dir/source/core/Backend.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNCore.dir/source/core/Backend.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNCore.dir/source/core/Backend.cpp.o: ../include/MNN/Rect.h
CMakeFiles/MNNCore.dir/source/core/Backend.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNCore.dir/source/core/Backend.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNCore.dir/source/core/Backend.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNCore.dir/source/core/Backend.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNCore.dir/source/core/Backend.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNCore.dir/source/core/Backend.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNCore.dir/source/core/Backend.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNCore.dir/source/core/Backend.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNCore.dir/source/core/Backend.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNCore.dir/source/core/Backend.cpp.o: ../source/backend/cpu/CPUTensorConvert.hpp
CMakeFiles/MNNCore.dir/source/core/Backend.cpp.o: ../source/backend/cpu/compute/CommonOptFunction.h
CMakeFiles/MNNCore.dir/source/core/Backend.cpp.o: ../source/backend/cpu/compute/Int8FunctionsOpt.h
CMakeFiles/MNNCore.dir/source/core/Backend.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNCore.dir/source/core/Backend.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNCore.dir/source/core/Backend.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNCore.dir/source/core/Backend.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNCore.dir/source/core/Backend.cpp.o: ../source/core/ConvolutionCommon.hpp
CMakeFiles/MNNCore.dir/source/core/Backend.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNCore.dir/source/core/Backend.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNCore.dir/source/core/Backend.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNCore.dir/source/core/Backend.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNCore.dir/source/core/Backend.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNCore.dir/source/core/Backend.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNCore.dir/source/core/Backend.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNCore.dir/source/core/Backend.cpp.o: ../source/core/Backend.cpp

CMakeFiles/MNNCore.dir/source/core/BufferAllocator.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNCore.dir/source/core/BufferAllocator.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNCore.dir/source/core/BufferAllocator.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNCore.dir/source/core/BufferAllocator.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNCore.dir/source/core/BufferAllocator.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNCore.dir/source/core/BufferAllocator.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNCore.dir/source/core/BufferAllocator.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNCore.dir/source/core/BufferAllocator.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNCore.dir/source/core/BufferAllocator.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNCore.dir/source/core/BufferAllocator.cpp.o: ../source/core/BufferAllocator.cpp
CMakeFiles/MNNCore.dir/source/core/BufferAllocator.cpp.o: ../source/core/MNNFileUtils.h

CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o: ../3rd_party/half/half.hpp
CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o: ../include/MNN/AutoTime.hpp
CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o: ../include/MNN/Rect.h
CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o: ../source/backend/cpu/CPUBackend.hpp
CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o: ../source/backend/cpu/ThreadPool.hpp
CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o: ../source/backend/cpu/compute/CommonOptFunction.h
CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o: ../source/backend/cpu/compute/Int8FunctionsOpt.h
CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o: ../source/core/ConvolutionCommon.hpp
CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o: ../source/core/OpCommonUtils.hpp
CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o: ../source/core/ConvolutionCommon.cpp
CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o: ../source/core/ConvolutionCommon.hpp
CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o: ../source/core/MNNFileUtils.h
CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNCore.dir/source/core/ConvolutionCommon.cpp.o: ../source/core/NonCopyable.hpp

CMakeFiles/MNNCore.dir/source/core/Execution.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNCore.dir/source/core/Execution.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNCore.dir/source/core/Execution.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNCore.dir/source/core/Execution.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNCore.dir/source/core/Execution.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNCore.dir/source/core/Execution.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNCore.dir/source/core/Execution.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNCore.dir/source/core/Execution.cpp.o: ../source/core/Execution.cpp

CMakeFiles/MNNCore.dir/source/core/FileLoader.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNCore.dir/source/core/FileLoader.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNCore.dir/source/core/FileLoader.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNNCore.dir/source/core/FileLoader.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNCore.dir/source/core/FileLoader.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNCore.dir/source/core/FileLoader.cpp.o: ../source/core/FileLoader.cpp

CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o: ../include/MNN/AutoTime.hpp
CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o: ../include/MNN/Interpreter.hpp
CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o: ../source/core/OpCommonUtils.hpp
CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o: ../source/core/Pipeline.hpp
CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o: ../source/core/RuntimeFactory.hpp
CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o: ../source/core/Schedule.hpp
CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o: ../source/core/Session.hpp
CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNCore.dir/source/core/Interpreter.cpp.o: ../source/core/Interpreter.cpp

CMakeFiles/MNNCore.dir/source/core/MNNFileUtils.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNCore.dir/source/core/MNNFileUtils.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNCore.dir/source/core/MNNFileUtils.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNCore.dir/source/core/MNNFileUtils.cpp.o: ../source/core/MNNFileUtils.cpp
CMakeFiles/MNNCore.dir/source/core/MNNFileUtils.cpp.o: ../source/core/MNNFileUtils.h

CMakeFiles/MNNCore.dir/source/core/MNNMemoryUtils.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNCore.dir/source/core/MNNMemoryUtils.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNCore.dir/source/core/MNNMemoryUtils.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNCore.dir/source/core/MNNMemoryUtils.cpp.o: ../source/core/MNNMemoryUtils.cpp

CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.o: ../source/core/OpCommonUtils.cpp
CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.o: ../source/core/OpCommonUtils.hpp
CMakeFiles/MNNCore.dir/source/core/OpCommonUtils.cpp.o: ../source/core/TensorUtils.hpp

CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o: ../include/MNN/AutoTime.hpp
CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o: ../include/MNN/Interpreter.hpp
CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o: ../source/backend/cpu/CPUBackend.hpp
CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o: ../source/backend/cpu/ThreadPool.hpp
CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o: ../source/backend/cpu/compute/Int8FunctionsOpt.h
CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o: ../source/core/ConvolutionCommon.hpp
CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o: ../source/core/OpCommonUtils.hpp
CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o: ../source/core/Pipeline.hpp
CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o: ../source/core/Schedule.hpp
CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o: ../source/core/WrapExecution.hpp
CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o: ../source/geometry/GeometryComputerUtils.hpp
CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNCore.dir/source/core/Pipeline.cpp.o: ../source/core/Pipeline.cpp

CMakeFiles/MNNCore.dir/source/core/RuntimeFactory.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNCore.dir/source/core/RuntimeFactory.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNCore.dir/source/core/RuntimeFactory.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNCore.dir/source/core/RuntimeFactory.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNCore.dir/source/core/RuntimeFactory.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNCore.dir/source/core/RuntimeFactory.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNCore.dir/source/core/RuntimeFactory.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNCore.dir/source/core/RuntimeFactory.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNCore.dir/source/core/RuntimeFactory.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNCore.dir/source/core/RuntimeFactory.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNCore.dir/source/core/RuntimeFactory.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNCore.dir/source/core/RuntimeFactory.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNCore.dir/source/core/RuntimeFactory.cpp.o: ../source/core/RuntimeFactory.hpp
CMakeFiles/MNNCore.dir/source/core/RuntimeFactory.cpp.o: ../source/core/RuntimeFactory.cpp

CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.o: ../include/MNN/AutoTime.hpp
CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.o: ../include/MNN/Interpreter.hpp
CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.o: ../source/core/RuntimeFactory.hpp
CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.o: ../source/core/Schedule.hpp
CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.o: ../source/geometry/GeometryComputerUtils.hpp
CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.o: ../source/utils/InitNet.hpp
CMakeFiles/MNNCore.dir/source/core/Schedule.cpp.o: ../source/core/Schedule.cpp

CMakeFiles/MNNCore.dir/source/core/Session.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNCore.dir/source/core/Session.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNCore.dir/source/core/Session.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNCore.dir/source/core/Session.cpp.o: ../include/MNN/AutoTime.hpp
CMakeFiles/MNNCore.dir/source/core/Session.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNCore.dir/source/core/Session.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNCore.dir/source/core/Session.cpp.o: ../include/MNN/Interpreter.hpp
CMakeFiles/MNNCore.dir/source/core/Session.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNCore.dir/source/core/Session.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNCore.dir/source/core/Session.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNCore.dir/source/core/Session.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNCore.dir/source/core/Session.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNCore.dir/source/core/Session.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNCore.dir/source/core/Session.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNCore.dir/source/core/Session.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNCore.dir/source/core/Session.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNCore.dir/source/core/Session.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNCore.dir/source/core/Session.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNCore.dir/source/core/Session.cpp.o: ../source/backend/cpu/CPUBackend.hpp
CMakeFiles/MNNCore.dir/source/core/Session.cpp.o: ../source/backend/cpu/ThreadPool.hpp
CMakeFiles/MNNCore.dir/source/core/Session.cpp.o: ../source/backend/cpu/compute/Int8FunctionsOpt.h
CMakeFiles/MNNCore.dir/source/core/Session.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNCore.dir/source/core/Session.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNCore.dir/source/core/Session.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNCore.dir/source/core/Session.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNCore.dir/source/core/Session.cpp.o: ../source/core/ConvolutionCommon.hpp
CMakeFiles/MNNCore.dir/source/core/Session.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNCore.dir/source/core/Session.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNCore.dir/source/core/Session.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNCore.dir/source/core/Session.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNCore.dir/source/core/Session.cpp.o: ../source/core/Pipeline.hpp
CMakeFiles/MNNCore.dir/source/core/Session.cpp.o: ../source/core/RuntimeFactory.hpp
CMakeFiles/MNNCore.dir/source/core/Session.cpp.o: ../source/core/Schedule.hpp
CMakeFiles/MNNCore.dir/source/core/Session.cpp.o: ../source/core/Session.hpp
CMakeFiles/MNNCore.dir/source/core/Session.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNCore.dir/source/core/Session.cpp.o: ../source/core/WrapExecution.hpp
CMakeFiles/MNNCore.dir/source/core/Session.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNCore.dir/source/core/Session.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNCore.dir/source/core/Session.cpp.o: ../source/utils/InitNet.hpp
CMakeFiles/MNNCore.dir/source/core/Session.cpp.o: ../source/core/Session.cpp

CMakeFiles/MNNCore.dir/source/core/Tensor.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNCore.dir/source/core/Tensor.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNCore.dir/source/core/Tensor.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNCore.dir/source/core/Tensor.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNCore.dir/source/core/Tensor.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNCore.dir/source/core/Tensor.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNCore.dir/source/core/Tensor.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNCore.dir/source/core/Tensor.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNCore.dir/source/core/Tensor.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNCore.dir/source/core/Tensor.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNCore.dir/source/core/Tensor.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNCore.dir/source/core/Tensor.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNCore.dir/source/core/Tensor.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNCore.dir/source/core/Tensor.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNCore.dir/source/core/Tensor.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNCore.dir/source/core/Tensor.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNCore.dir/source/core/Tensor.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNCore.dir/source/core/Tensor.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNCore.dir/source/core/Tensor.cpp.o: ../source/core/Tensor.cpp

CMakeFiles/MNNCore.dir/source/core/TensorUtils.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNCore.dir/source/core/TensorUtils.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNCore.dir/source/core/TensorUtils.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNCore.dir/source/core/TensorUtils.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNCore.dir/source/core/TensorUtils.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNCore.dir/source/core/TensorUtils.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNCore.dir/source/core/TensorUtils.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNCore.dir/source/core/TensorUtils.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNCore.dir/source/core/TensorUtils.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNCore.dir/source/core/TensorUtils.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNCore.dir/source/core/TensorUtils.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNCore.dir/source/core/TensorUtils.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNCore.dir/source/core/TensorUtils.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNCore.dir/source/core/TensorUtils.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNCore.dir/source/core/TensorUtils.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNCore.dir/source/core/TensorUtils.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNCore.dir/source/core/TensorUtils.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNCore.dir/source/core/TensorUtils.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNCore.dir/source/core/TensorUtils.cpp.o: ../source/core/TensorUtils.cpp

CMakeFiles/MNNCore.dir/source/core/WorkerThread.cpp.o: ../include/MNN/AutoTime.hpp
CMakeFiles/MNNCore.dir/source/core/WorkerThread.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNCore.dir/source/core/WorkerThread.cpp.o: ../source/core/WorkerThread.cpp
CMakeFiles/MNNCore.dir/source/core/WorkerThread.cpp.o: ../source/core/WorkerThread.hpp

CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o: ../include/MNN/AutoTime.hpp
CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o: ../include/MNN/Rect.h
CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o: ../source/backend/cpu/CPUBackend.hpp
CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o: ../source/backend/cpu/CPUCast.hpp
CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o: ../source/backend/cpu/ThreadPool.hpp
CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o: ../source/backend/cpu/compute/CommonOptFunction.h
CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o: ../source/backend/cpu/compute/Int8FunctionsOpt.h
CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o: ../source/core/Concurrency.h
CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o: ../source/core/ConvolutionCommon.hpp
CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o: ../source/core/OpCommonUtils.hpp
CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o: ../source/core/WrapExecution.hpp
CMakeFiles/MNNCore.dir/source/core/WrapExecution.cpp.o: ../source/core/WrapExecution.cpp

