#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

../3rd_party/flatbuffers/include/flatbuffers/base.h
assert.h
-
cstdint
-
cstddef
-
cstdlib
-
cstring
-
crtdbg.h
-
utility.h
-
utility
-
string
-
type_traits
-
vector
-
set
-
algorithm
-
iterator
-
memory
-
functional
-
stl_emulation.h
../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
string_view
-
experimental/string_view
-

../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
base.h
../3rd_party/flatbuffers/include/flatbuffers/base.h
cmath
-

../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
string
-
type_traits
-
vector
-
memory
-
limits
-
cctype
-

../include/MNN/ErrorCode.hpp

../include/MNN/HalideRuntime.h
stddef.h
-
stdint.h
-
stdbool.h
-

../include/MNN/MNNDefine.h
assert.h
-
stdio.h
-
TargetConditionals.h
-
hilog/log.h
-
android/log.h
-
syslog.h
-

../include/MNN/MNNForwardType.h
stdint.h
-
stddef.h
-

../include/MNN/Rect.h
math.h
-
algorithm
-
utility
-
MNN/MNNDefine.h
-

../include/MNN/Tensor.hpp
vector
-
MNN/HalideRuntime.h
-
MNN/MNNDefine.h
-

../schema/current/CaffeOp_generated.h
Tensor_generated.h
../schema/current/Tensor_generated.h
Type_generated.h
../schema/current/Type_generated.h

../schema/current/ExtraInfo_generated.h
Tensor_generated.h
../schema/current/Tensor_generated.h
Type_generated.h
../schema/current/Type_generated.h

../schema/current/MNN_generated.h
CaffeOp_generated.h
../schema/current/CaffeOp_generated.h
ExtraInfo_generated.h
../schema/current/ExtraInfo_generated.h
TFQuantizeOp_generated.h
../schema/current/TFQuantizeOp_generated.h
Tensor_generated.h
../schema/current/Tensor_generated.h
TensorflowOp_generated.h
../schema/current/TensorflowOp_generated.h
Type_generated.h
../schema/current/Type_generated.h
UserDefine_generated.h
../schema/current/UserDefine_generated.h

../schema/current/TFQuantizeOp_generated.h
CaffeOp_generated.h
../schema/current/CaffeOp_generated.h
Tensor_generated.h
../schema/current/Tensor_generated.h
Type_generated.h
../schema/current/Type_generated.h

../schema/current/Tensor_generated.h
Type_generated.h
../schema/current/Type_generated.h

../schema/current/TensorflowOp_generated.h
Tensor_generated.h
../schema/current/Tensor_generated.h
Type_generated.h
../schema/current/Type_generated.h

../schema/current/Type_generated.h
flatbuffers/flatbuffers.h
../schema/current/flatbuffers/flatbuffers.h

../schema/current/UserDefine_generated.h
Tensor_generated.h
../schema/current/Tensor_generated.h
Type_generated.h
../schema/current/Type_generated.h

../source/backend/cpu/compute/CommonOptFunction.h
stdint.h
-
stdio.h
-
string.h
-
vector
-
MNN/Rect.h
-
core/Macro.h
../source/backend/cpu/compute/core/Macro.h
backend/cpu/compute/Int8FunctionsOpt.h
../source/backend/cpu/compute/backend/cpu/compute/Int8FunctionsOpt.h

../source/backend/cpu/compute/ConvOpt.h
stdint.h
-
stdio.h
-

../source/backend/cpu/compute/Int8FunctionsOpt.h
stdint.h
-
stdio.h
-
sys/types.h
-
core/Macro.h
../source/backend/cpu/compute/core/Macro.h
core/ConvolutionCommon.hpp
../source/backend/cpu/compute/core/ConvolutionCommon.hpp
BaseTsd.h
-

../source/core/AutoStorage.h
stdint.h
-
string.h
-
MNNMemoryUtils.h
../source/core/MNNMemoryUtils.h

../source/core/Backend.hpp
MNN/MNNForwardType.h
-
MNN/ErrorCode.hpp
-
map
-
Command.hpp
../source/core/Command.hpp
NonCopyable.hpp
../source/core/NonCopyable.hpp
BufferAllocator.hpp
../source/core/BufferAllocator.hpp
future
-
atomic
-

../source/core/BufferAllocator.hpp
map
-
set
-
memory
-
vector
-
MNNMemoryUtils.h
../source/core/MNNMemoryUtils.h
NonCopyable.hpp
../source/core/NonCopyable.hpp
AutoStorage.h
../source/core/AutoStorage.h
MNN/Tensor.hpp
-
MNN/ErrorCode.hpp
-

../source/core/Command.hpp
MNN/Tensor.hpp
-
AutoStorage.h
../source/core/AutoStorage.h
string
-
memory
-

../source/core/ConvolutionCommon.hpp
AutoStorage.h
../source/core/AutoStorage.h
Execution.hpp
../source/core/Execution.hpp
MNN_generated.h
../source/core/MNN_generated.h

../source/core/Execution.hpp
MNN/MNNForwardType.h
-
MNN/ErrorCode.hpp
-
MNN/Tensor.hpp
-
memory
-
string
-
NonCopyable.hpp
../source/core/NonCopyable.hpp

../source/core/MNNMemoryUtils.h
stdio.h
-
core/Macro.h
../source/core/core/Macro.h

../source/core/Macro.h
MNN/MNNDefine.h
-
BaseTsd.h
-

../source/core/NonCopyable.hpp

../source/core/TensorUtils.hpp
MNN/Tensor.hpp
-
Backend.hpp
../source/core/Backend.hpp
AutoStorage.h
../source/core/AutoStorage.h
Tensor_generated.h
../source/core/Tensor_generated.h

../source/math/Matrix.hpp
stdio.h
-
memory
-
MNN/Tensor.hpp
-

../source/math/WingoradGenerater.hpp
memory
-
vector
-
math/Matrix.hpp
../source/math/math/Matrix.hpp

/root/pan/shunzao_ai_lib-develop/MNN-master/source/math/Matrix.cpp
math/Matrix.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/math/math/Matrix.hpp
core/MNNMemoryUtils.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/math/core/MNNMemoryUtils.h
core/Macro.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/math/core/Macro.h
core/TensorUtils.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/math/core/TensorUtils.hpp
backend/cpu/compute/CommonOptFunction.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/math/backend/cpu/compute/CommonOptFunction.h
backend/cpu/compute/ConvOpt.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/math/backend/cpu/compute/ConvOpt.h
cmath
-
arm_neon.h
-

/root/pan/shunzao_ai_lib-develop/MNN-master/source/math/WingoradGenerater.cpp
math.h
-
string.h
-
math/WingoradGenerater.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/math/math/WingoradGenerater.hpp
core/Macro.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/math/core/Macro.h

