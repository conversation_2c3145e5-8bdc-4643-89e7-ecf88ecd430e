# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /root/pan/shunzao_ai_lib-develop/MNN-master

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm

# Include any dependencies generated for this target.
include CMakeFiles/MNNMath.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/MNNMath.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/MNNMath.dir/flags.make

CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.o: CMakeFiles/MNNMath.dir/flags.make
CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.o: ../source/math/Matrix.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/math/Matrix.cpp

CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/math/Matrix.cpp > CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.i

CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/math/Matrix.cpp -o CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.s

CMakeFiles/MNNMath.dir/source/math/WingoradGenerater.cpp.o: CMakeFiles/MNNMath.dir/flags.make
CMakeFiles/MNNMath.dir/source/math/WingoradGenerater.cpp.o: ../source/math/WingoradGenerater.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/MNNMath.dir/source/math/WingoradGenerater.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNMath.dir/source/math/WingoradGenerater.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/math/WingoradGenerater.cpp

CMakeFiles/MNNMath.dir/source/math/WingoradGenerater.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNMath.dir/source/math/WingoradGenerater.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/math/WingoradGenerater.cpp > CMakeFiles/MNNMath.dir/source/math/WingoradGenerater.cpp.i

CMakeFiles/MNNMath.dir/source/math/WingoradGenerater.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNMath.dir/source/math/WingoradGenerater.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/math/WingoradGenerater.cpp -o CMakeFiles/MNNMath.dir/source/math/WingoradGenerater.cpp.s

MNNMath: CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.o
MNNMath: CMakeFiles/MNNMath.dir/source/math/WingoradGenerater.cpp.o
MNNMath: CMakeFiles/MNNMath.dir/build.make

.PHONY : MNNMath

# Rule to build all files generated by this target.
CMakeFiles/MNNMath.dir/build: MNNMath

.PHONY : CMakeFiles/MNNMath.dir/build

CMakeFiles/MNNMath.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/MNNMath.dir/cmake_clean.cmake
.PHONY : CMakeFiles/MNNMath.dir/clean

CMakeFiles/MNNMath.dir/depend:
	cd /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /root/pan/shunzao_ai_lib-develop/MNN-master /root/pan/shunzao_ai_lib-develop/MNN-master /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNMath.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/MNNMath.dir/depend

