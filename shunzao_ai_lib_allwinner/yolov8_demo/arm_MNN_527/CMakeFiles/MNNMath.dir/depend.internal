# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.o
 ../3rd_party/flatbuffers/include/flatbuffers/base.h
 ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
 ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
 ../include/MNN/ErrorCode.hpp
 ../include/MNN/HalideRuntime.h
 ../include/MNN/MNNDefine.h
 ../include/MNN/MNNForwardType.h
 ../include/MNN/Rect.h
 ../include/MNN/Tensor.hpp
 ../schema/current/CaffeOp_generated.h
 ../schema/current/ExtraInfo_generated.h
 ../schema/current/MNN_generated.h
 ../schema/current/TFQuantizeOp_generated.h
 ../schema/current/Tensor_generated.h
 ../schema/current/TensorflowOp_generated.h
 ../schema/current/Type_generated.h
 ../schema/current/UserDefine_generated.h
 ../source/backend/cpu/compute/CommonOptFunction.h
 ../source/backend/cpu/compute/ConvOpt.h
 ../source/backend/cpu/compute/Int8FunctionsOpt.h
 ../source/core/AutoStorage.h
 ../source/core/Backend.hpp
 ../source/core/BufferAllocator.hpp
 ../source/core/Command.hpp
 ../source/core/ConvolutionCommon.hpp
 ../source/core/Execution.hpp
 ../source/core/MNNMemoryUtils.h
 ../source/core/Macro.h
 ../source/core/NonCopyable.hpp
 ../source/core/TensorUtils.hpp
 ../source/math/Matrix.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/math/Matrix.cpp
CMakeFiles/MNNMath.dir/source/math/WingoradGenerater.cpp.o
 ../include/MNN/HalideRuntime.h
 ../include/MNN/MNNDefine.h
 ../include/MNN/Tensor.hpp
 ../source/core/Macro.h
 ../source/math/Matrix.hpp
 ../source/math/WingoradGenerater.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/math/WingoradGenerater.cpp
