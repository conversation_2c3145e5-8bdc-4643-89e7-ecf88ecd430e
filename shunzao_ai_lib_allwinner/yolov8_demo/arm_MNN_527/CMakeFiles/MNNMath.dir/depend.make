# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.o: ../include/MNN/Rect.h
CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.o: ../source/backend/cpu/compute/CommonOptFunction.h
CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.o: ../source/backend/cpu/compute/ConvOpt.h
CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.o: ../source/backend/cpu/compute/Int8FunctionsOpt.h
CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.o: ../source/core/ConvolutionCommon.hpp
CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.o: ../source/math/Matrix.hpp
CMakeFiles/MNNMath.dir/source/math/Matrix.cpp.o: ../source/math/Matrix.cpp

CMakeFiles/MNNMath.dir/source/math/WingoradGenerater.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNMath.dir/source/math/WingoradGenerater.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNMath.dir/source/math/WingoradGenerater.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNMath.dir/source/math/WingoradGenerater.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNMath.dir/source/math/WingoradGenerater.cpp.o: ../source/math/Matrix.hpp
CMakeFiles/MNNMath.dir/source/math/WingoradGenerater.cpp.o: ../source/math/WingoradGenerater.hpp
CMakeFiles/MNNMath.dir/source/math/WingoradGenerater.cpp.o: ../source/math/WingoradGenerater.cpp

