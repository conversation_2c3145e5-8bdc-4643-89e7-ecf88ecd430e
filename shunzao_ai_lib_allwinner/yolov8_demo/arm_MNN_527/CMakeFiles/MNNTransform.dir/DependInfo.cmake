# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "CXX"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_CXX
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/ConvertUtils.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/ConvertUtils.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryBatchMatMul.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryBinary.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryBroadcastTo.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometryBroadcastTo.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryComputer.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryComputerUtils.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryConcat.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometryConcat.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryConv2D.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryConv2DBackPropFilter.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryConv3D.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryConvUtils.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryConvert.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryCosineSimilarity.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryCrop.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometryCrop.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryCumSum.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryDepthToSpace.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometryDepthToSpace.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryDet.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometryDet.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryDilation2D.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryELU.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryFill.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometryFill.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryGather.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryImageOp.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryInnerProduct.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryLRN.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryLSTM.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometryLSTM.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryLayernorm.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryOPRegister.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometryOPRegister.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryPermute.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometryPermute.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryPoolGrad.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryPooling3D.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryReduce.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryReshape.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometryReshape.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryReverseSequence.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometryReverseSequence.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryScatter.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometrySelect.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometrySelect.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryShape.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometrySlice.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometrySlice.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometrySpaceToBatchND.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpaceToBatchND.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometrySpatialProduct.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryStridedSlice.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometryStridedSlice.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryTensorArray.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryThreshold.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryTile.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometryTile.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryTopK.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryUnary.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeArgMax.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeArgMax.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeAttention.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeAttention.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeBatchToSpaceND.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeBatchToSpaceND.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeBinaryOp.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeBinaryOp.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeBroadcastTo.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeBroadcastTo.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeCast.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeCast.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeConcat.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeConcat.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeConvTranspose3D.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeConvTranspose3D.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeConvolution.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeConvolution3D.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution3D.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeCosineSimilarity.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeCosineSimilarity.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeCrop.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeCrop.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeCropAndResize.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeCropAndResize.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeDeconvolution.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeDeconvolution.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeDepthToSpace.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeDepthToSpace.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeDequantize.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeDequantize.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeDet.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeDet.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeDetectionOutput.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionOutput.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeDetectionPostProcess.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionPostProcess.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeDynamicQuant.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeDynamicQuant.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeExpandDims.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeExpandDims.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeFill.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeFill.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeGatherND.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherND.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeGatherV2.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherV2.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeGridSample.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeGridSample.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeHistogram.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeHistogram.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeInnerProduct.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeInnerProduct.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeInterp.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeInterp.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeLSTM.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeLSTM.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeLinSpace.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeLinSpace.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeMatMul.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeMatMul.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeMoments.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeMoments.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeNonMaxSuppressionV2.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeNonMaxSuppressionV2.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeOneHot.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeOneHot.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapePack.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapePack.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapePadding.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapePadding.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapePermute.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapePermute.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapePlugin.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapePlugin.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapePool.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapePool.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapePool3D.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapePool3D.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapePriorbox.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapePriorbox.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeProposal.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeProposal.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeQuantizedAvgPool.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedAvgPool.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeQuantizedMaxPool.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedMaxPool.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeRNNSequenceGRU.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeRNNSequenceGRU.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeROIAlign.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeROIAlign.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeROIPooling.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeROIPooling.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeRandomUniform.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeRandomUniform.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeRange.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeRange.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeReduction.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeReduction.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeRegister.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeRegister.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeReshape.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeReshape.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeResize.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeResize.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeScatterNd.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeScatterNd.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSegmentMean.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeSegmentMean.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSelect.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeSelect.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSetDiff1D.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeSetDiff1D.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeShape.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeShape.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSize.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeSize.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSlice.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeSlice.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSliceTf.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeSliceTf.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSpaceToBatchND.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToBatchND.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSpaceToDepth.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToDepth.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSplitGelu.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeSplitGelu.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSqueeze.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeSqueeze.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeStft.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeStft.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeStridedSlice.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeStridedSlice.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSvd.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeSvd.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeTensorArray.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorArray.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeTensorConvert.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorConvert.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeTile.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeTile.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeTopKV2.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeTopKV2.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeTranspose.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeTranspose.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeUnique.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeUnique.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeUnpack.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeUnpack.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeUnravelIndex.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeUnravelIndex.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeWhere.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/ShapeWhere.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/SizeComputer.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/SizeComputer.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/render/ShapeRasterAndInterpolate.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/source/shape/render/ShapeRasterAndInterpolate.cpp.o"
  )
set(CMAKE_CXX_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_CXX
  "MNN_KLEIDIAI_ENABLED=1"
  "MNN_SME2"
  "MNN_SUPPORT_DEPRECATED_OPV2"
  "MNN_SUPPORT_QUANT_EXTEND"
  "MNN_USE_NEON"
  "MNN_USE_THREAD_POOL"
  )

# The include file search paths:
set(CMAKE_CXX_TARGET_INCLUDE_PATH
  "../include"
  "../source"
  "../express"
  "../tools"
  "../codegen"
  "../schema/current"
  "../3rd_party"
  "../3rd_party/flatbuffers/include"
  "../3rd_party/half"
  "../3rd_party/imageHelper"
  "../3rd_party/OpenCLHeaders"
  "_deps/kleidiai-v1.9.0"
  "_deps/kleidiai-v1.9.0/kai"
  "_deps/kleidiai-v1.9.0/kai/ukernels"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/matmul_clamp_f32_qai8dxp_qsi4cxp"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/matmul_clamp_f16_qsi8d32p_qai4c32p"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/matmul_clamp_f32_qsi8d32p_qai4c32p"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/pack"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/matmul_clamp_f32_f32p_f32p"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/matmul_clamp_f32_f32_f32p"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/matmul_clamp_f16_f16p_f16p"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/matmul_clamp_f16_f16_f16p"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/imatmul_clamp_f32_f32p_f32p"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/imatmul_clamp_f16_f16p_f16p"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
