# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /root/pan/shunzao_ai_lib-develop/MNN-master

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm

# Include any dependencies generated for this target.
include CMakeFiles/MNNTransform.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/MNNTransform.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/MNNTransform.dir/flags.make

CMakeFiles/MNNTransform.dir/source/geometry/ConvertUtils.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/ConvertUtils.cpp.o: ../source/geometry/ConvertUtils.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/ConvertUtils.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/ConvertUtils.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/ConvertUtils.cpp

CMakeFiles/MNNTransform.dir/source/geometry/ConvertUtils.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/ConvertUtils.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/ConvertUtils.cpp > CMakeFiles/MNNTransform.dir/source/geometry/ConvertUtils.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/ConvertUtils.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/ConvertUtils.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/ConvertUtils.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/ConvertUtils.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.o: ../source/geometry/GeometryBatchMatMul.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryBatchMatMul.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryBatchMatMul.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryBatchMatMul.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.o: ../source/geometry/GeometryBinary.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryBinary.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryBinary.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryBinary.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometryBroadcastTo.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBroadcastTo.cpp.o: ../source/geometry/GeometryBroadcastTo.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometryBroadcastTo.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryBroadcastTo.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryBroadcastTo.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryBroadcastTo.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometryBroadcastTo.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryBroadcastTo.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometryBroadcastTo.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometryBroadcastTo.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometryBroadcastTo.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryBroadcastTo.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryBroadcastTo.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.o: ../source/geometry/GeometryComputer.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryComputer.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryComputer.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryComputer.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.o: ../source/geometry/GeometryComputerUtils.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryComputerUtils.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryComputerUtils.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryComputerUtils.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometryConcat.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConcat.cpp.o: ../source/geometry/GeometryConcat.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometryConcat.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryConcat.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryConcat.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryConcat.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometryConcat.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryConcat.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometryConcat.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometryConcat.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometryConcat.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryConcat.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryConcat.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.o: ../source/geometry/GeometryConv2D.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryConv2D.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryConv2D.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryConv2D.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.o: ../source/geometry/GeometryConv2DBackPropFilter.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryConv2DBackPropFilter.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryConv2DBackPropFilter.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryConv2DBackPropFilter.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.o: ../source/geometry/GeometryConv3D.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryConv3D.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryConv3D.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryConv3D.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.o: ../source/geometry/GeometryConvUtils.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryConvUtils.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryConvUtils.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryConvUtils.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.o: ../source/geometry/GeometryConvert.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryConvert.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryConvert.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryConvert.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.o: ../source/geometry/GeometryCosineSimilarity.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryCosineSimilarity.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryCosineSimilarity.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryCosineSimilarity.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometryCrop.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCrop.cpp.o: ../source/geometry/GeometryCrop.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometryCrop.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryCrop.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryCrop.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryCrop.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometryCrop.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryCrop.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometryCrop.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometryCrop.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometryCrop.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryCrop.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryCrop.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.o: ../source/geometry/GeometryCumSum.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryCumSum.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryCumSum.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryCumSum.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometryDepthToSpace.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDepthToSpace.cpp.o: ../source/geometry/GeometryDepthToSpace.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometryDepthToSpace.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryDepthToSpace.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryDepthToSpace.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryDepthToSpace.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometryDepthToSpace.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryDepthToSpace.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometryDepthToSpace.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometryDepthToSpace.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometryDepthToSpace.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryDepthToSpace.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryDepthToSpace.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometryDet.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDet.cpp.o: ../source/geometry/GeometryDet.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometryDet.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryDet.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryDet.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryDet.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometryDet.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryDet.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometryDet.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometryDet.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometryDet.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryDet.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryDet.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.o: ../source/geometry/GeometryDilation2D.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryDilation2D.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryDilation2D.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryDilation2D.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.o: ../source/geometry/GeometryELU.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryELU.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryELU.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryELU.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometryFill.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometryFill.cpp.o: ../source/geometry/GeometryFill.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometryFill.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryFill.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryFill.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryFill.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometryFill.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryFill.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometryFill.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometryFill.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometryFill.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryFill.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryFill.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.o: ../source/geometry/GeometryGather.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_21) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryGather.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryGather.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryGather.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.o: ../source/geometry/GeometryImageOp.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_22) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryImageOp.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryImageOp.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryImageOp.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.o: ../source/geometry/GeometryInnerProduct.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_23) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryInnerProduct.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryInnerProduct.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryInnerProduct.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.o: ../source/geometry/GeometryLRN.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_24) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryLRN.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryLRN.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryLRN.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometryLSTM.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLSTM.cpp.o: ../source/geometry/GeometryLSTM.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_25) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometryLSTM.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryLSTM.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryLSTM.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryLSTM.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometryLSTM.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryLSTM.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometryLSTM.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometryLSTM.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometryLSTM.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryLSTM.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryLSTM.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.o: ../source/geometry/GeometryLayernorm.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_26) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryLayernorm.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryLayernorm.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryLayernorm.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometryOPRegister.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometryOPRegister.cpp.o: ../source/geometry/GeometryOPRegister.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_27) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometryOPRegister.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryOPRegister.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryOPRegister.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryOPRegister.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometryOPRegister.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryOPRegister.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometryOPRegister.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometryOPRegister.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometryOPRegister.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryOPRegister.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryOPRegister.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometryPermute.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPermute.cpp.o: ../source/geometry/GeometryPermute.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_28) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometryPermute.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryPermute.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryPermute.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryPermute.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometryPermute.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryPermute.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometryPermute.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometryPermute.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometryPermute.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryPermute.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryPermute.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.o: ../source/geometry/GeometryPoolGrad.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_29) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryPoolGrad.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryPoolGrad.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryPoolGrad.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.o: ../source/geometry/GeometryPooling3D.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_30) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryPooling3D.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryPooling3D.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryPooling3D.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.o: ../source/geometry/GeometryReduce.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_31) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryReduce.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryReduce.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryReduce.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometryReshape.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReshape.cpp.o: ../source/geometry/GeometryReshape.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_32) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometryReshape.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryReshape.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryReshape.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryReshape.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometryReshape.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryReshape.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometryReshape.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometryReshape.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometryReshape.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryReshape.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryReshape.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometryReverseSequence.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReverseSequence.cpp.o: ../source/geometry/GeometryReverseSequence.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_33) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometryReverseSequence.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryReverseSequence.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryReverseSequence.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryReverseSequence.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometryReverseSequence.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryReverseSequence.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometryReverseSequence.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometryReverseSequence.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometryReverseSequence.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryReverseSequence.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryReverseSequence.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.o: ../source/geometry/GeometryScatter.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_34) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryScatter.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryScatter.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryScatter.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometrySelect.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySelect.cpp.o: ../source/geometry/GeometrySelect.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_35) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometrySelect.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometrySelect.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometrySelect.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometrySelect.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometrySelect.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometrySelect.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometrySelect.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometrySelect.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometrySelect.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometrySelect.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometrySelect.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.o: ../source/geometry/GeometryShape.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_36) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryShape.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryShape.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryShape.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometrySlice.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySlice.cpp.o: ../source/geometry/GeometrySlice.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_37) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometrySlice.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometrySlice.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometrySlice.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometrySlice.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometrySlice.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometrySlice.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometrySlice.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometrySlice.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometrySlice.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometrySlice.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometrySlice.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpaceToBatchND.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpaceToBatchND.cpp.o: ../source/geometry/GeometrySpaceToBatchND.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_38) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpaceToBatchND.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpaceToBatchND.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometrySpaceToBatchND.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpaceToBatchND.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpaceToBatchND.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometrySpaceToBatchND.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpaceToBatchND.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpaceToBatchND.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpaceToBatchND.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometrySpaceToBatchND.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpaceToBatchND.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.o: ../source/geometry/GeometrySpatialProduct.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_39) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometrySpatialProduct.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometrySpatialProduct.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometrySpatialProduct.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometryStridedSlice.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometryStridedSlice.cpp.o: ../source/geometry/GeometryStridedSlice.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_40) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometryStridedSlice.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryStridedSlice.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryStridedSlice.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryStridedSlice.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometryStridedSlice.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryStridedSlice.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometryStridedSlice.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometryStridedSlice.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometryStridedSlice.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryStridedSlice.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryStridedSlice.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.o: ../source/geometry/GeometryTensorArray.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_41) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryTensorArray.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryTensorArray.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryTensorArray.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.o: ../source/geometry/GeometryThreshold.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_42) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryThreshold.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryThreshold.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryThreshold.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometryTile.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTile.cpp.o: ../source/geometry/GeometryTile.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_43) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometryTile.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryTile.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryTile.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryTile.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometryTile.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryTile.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometryTile.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometryTile.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometryTile.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryTile.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryTile.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.o: ../source/geometry/GeometryTopK.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_44) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryTopK.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryTopK.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryTopK.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.s

CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.o: ../source/geometry/GeometryUnary.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_45) "Building CXX object CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryUnary.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryUnary.cpp > CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.i

CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/geometry/GeometryUnary.cpp -o CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeArgMax.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeArgMax.cpp.o: ../source/shape/ShapeArgMax.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_46) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeArgMax.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeArgMax.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeArgMax.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeArgMax.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeArgMax.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeArgMax.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeArgMax.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeArgMax.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeArgMax.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeArgMax.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeArgMax.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeAttention.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeAttention.cpp.o: ../source/shape/ShapeAttention.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_47) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeAttention.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeAttention.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeAttention.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeAttention.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeAttention.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeAttention.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeAttention.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeAttention.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeAttention.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeAttention.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeAttention.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeBatchToSpaceND.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeBatchToSpaceND.cpp.o: ../source/shape/ShapeBatchToSpaceND.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_48) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeBatchToSpaceND.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeBatchToSpaceND.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeBatchToSpaceND.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeBatchToSpaceND.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeBatchToSpaceND.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeBatchToSpaceND.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeBatchToSpaceND.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeBatchToSpaceND.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeBatchToSpaceND.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeBatchToSpaceND.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeBatchToSpaceND.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeBinaryOp.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeBinaryOp.cpp.o: ../source/shape/ShapeBinaryOp.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_49) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeBinaryOp.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeBinaryOp.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeBinaryOp.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeBinaryOp.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeBinaryOp.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeBinaryOp.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeBinaryOp.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeBinaryOp.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeBinaryOp.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeBinaryOp.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeBinaryOp.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeBroadcastTo.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeBroadcastTo.cpp.o: ../source/shape/ShapeBroadcastTo.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_50) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeBroadcastTo.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeBroadcastTo.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeBroadcastTo.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeBroadcastTo.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeBroadcastTo.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeBroadcastTo.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeBroadcastTo.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeBroadcastTo.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeBroadcastTo.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeBroadcastTo.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeBroadcastTo.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeCast.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeCast.cpp.o: ../source/shape/ShapeCast.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_51) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeCast.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeCast.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeCast.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeCast.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeCast.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeCast.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeCast.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeCast.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeCast.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeCast.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeCast.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeConcat.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeConcat.cpp.o: ../source/shape/ShapeConcat.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_52) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeConcat.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeConcat.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeConcat.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeConcat.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeConcat.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeConcat.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeConcat.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeConcat.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeConcat.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeConcat.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeConcat.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeConvTranspose3D.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvTranspose3D.cpp.o: ../source/shape/ShapeConvTranspose3D.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_53) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeConvTranspose3D.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeConvTranspose3D.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeConvTranspose3D.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeConvTranspose3D.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeConvTranspose3D.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeConvTranspose3D.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeConvTranspose3D.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeConvTranspose3D.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeConvTranspose3D.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeConvTranspose3D.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeConvTranspose3D.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution.cpp.o: ../source/shape/ShapeConvolution.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_54) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeConvolution.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeConvolution.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeConvolution.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution3D.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution3D.cpp.o: ../source/shape/ShapeConvolution3D.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_55) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution3D.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution3D.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeConvolution3D.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution3D.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution3D.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeConvolution3D.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution3D.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution3D.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution3D.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeConvolution3D.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution3D.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeCosineSimilarity.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeCosineSimilarity.cpp.o: ../source/shape/ShapeCosineSimilarity.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_56) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeCosineSimilarity.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeCosineSimilarity.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeCosineSimilarity.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeCosineSimilarity.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeCosineSimilarity.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeCosineSimilarity.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeCosineSimilarity.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeCosineSimilarity.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeCosineSimilarity.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeCosineSimilarity.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeCosineSimilarity.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeCrop.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeCrop.cpp.o: ../source/shape/ShapeCrop.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_57) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeCrop.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeCrop.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeCrop.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeCrop.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeCrop.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeCrop.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeCrop.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeCrop.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeCrop.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeCrop.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeCrop.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeCropAndResize.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeCropAndResize.cpp.o: ../source/shape/ShapeCropAndResize.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_58) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeCropAndResize.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeCropAndResize.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeCropAndResize.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeCropAndResize.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeCropAndResize.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeCropAndResize.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeCropAndResize.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeCropAndResize.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeCropAndResize.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeCropAndResize.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeCropAndResize.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeDeconvolution.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeDeconvolution.cpp.o: ../source/shape/ShapeDeconvolution.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_59) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeDeconvolution.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeDeconvolution.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeDeconvolution.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeDeconvolution.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeDeconvolution.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeDeconvolution.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeDeconvolution.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeDeconvolution.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeDeconvolution.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeDeconvolution.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeDeconvolution.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeDepthToSpace.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeDepthToSpace.cpp.o: ../source/shape/ShapeDepthToSpace.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_60) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeDepthToSpace.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeDepthToSpace.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeDepthToSpace.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeDepthToSpace.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeDepthToSpace.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeDepthToSpace.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeDepthToSpace.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeDepthToSpace.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeDepthToSpace.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeDepthToSpace.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeDepthToSpace.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeDequantize.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeDequantize.cpp.o: ../source/shape/ShapeDequantize.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_61) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeDequantize.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeDequantize.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeDequantize.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeDequantize.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeDequantize.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeDequantize.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeDequantize.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeDequantize.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeDequantize.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeDequantize.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeDequantize.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeDet.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeDet.cpp.o: ../source/shape/ShapeDet.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_62) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeDet.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeDet.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeDet.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeDet.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeDet.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeDet.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeDet.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeDet.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeDet.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeDet.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeDet.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionOutput.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionOutput.cpp.o: ../source/shape/ShapeDetectionOutput.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_63) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionOutput.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionOutput.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeDetectionOutput.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionOutput.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionOutput.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeDetectionOutput.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionOutput.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionOutput.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionOutput.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeDetectionOutput.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionOutput.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionPostProcess.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionPostProcess.cpp.o: ../source/shape/ShapeDetectionPostProcess.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_64) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionPostProcess.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionPostProcess.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeDetectionPostProcess.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionPostProcess.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionPostProcess.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeDetectionPostProcess.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionPostProcess.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionPostProcess.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionPostProcess.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeDetectionPostProcess.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionPostProcess.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeDynamicQuant.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeDynamicQuant.cpp.o: ../source/shape/ShapeDynamicQuant.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_65) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeDynamicQuant.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeDynamicQuant.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeDynamicQuant.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeDynamicQuant.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeDynamicQuant.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeDynamicQuant.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeDynamicQuant.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeDynamicQuant.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeDynamicQuant.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeDynamicQuant.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeDynamicQuant.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeExpandDims.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeExpandDims.cpp.o: ../source/shape/ShapeExpandDims.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_66) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeExpandDims.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeExpandDims.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeExpandDims.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeExpandDims.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeExpandDims.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeExpandDims.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeExpandDims.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeExpandDims.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeExpandDims.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeExpandDims.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeExpandDims.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeFill.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeFill.cpp.o: ../source/shape/ShapeFill.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_67) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeFill.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeFill.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeFill.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeFill.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeFill.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeFill.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeFill.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeFill.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeFill.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeFill.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeFill.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherND.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherND.cpp.o: ../source/shape/ShapeGatherND.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_68) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherND.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherND.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeGatherND.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherND.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherND.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeGatherND.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherND.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherND.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherND.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeGatherND.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherND.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherV2.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherV2.cpp.o: ../source/shape/ShapeGatherV2.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_69) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherV2.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherV2.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeGatherV2.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherV2.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherV2.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeGatherV2.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherV2.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherV2.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherV2.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeGatherV2.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherV2.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeGridSample.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeGridSample.cpp.o: ../source/shape/ShapeGridSample.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_70) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeGridSample.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeGridSample.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeGridSample.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeGridSample.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeGridSample.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeGridSample.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeGridSample.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeGridSample.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeGridSample.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeGridSample.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeGridSample.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeHistogram.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeHistogram.cpp.o: ../source/shape/ShapeHistogram.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_71) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeHistogram.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeHistogram.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeHistogram.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeHistogram.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeHistogram.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeHistogram.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeHistogram.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeHistogram.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeHistogram.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeHistogram.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeHistogram.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeInnerProduct.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeInnerProduct.cpp.o: ../source/shape/ShapeInnerProduct.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_72) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeInnerProduct.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeInnerProduct.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeInnerProduct.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeInnerProduct.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeInnerProduct.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeInnerProduct.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeInnerProduct.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeInnerProduct.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeInnerProduct.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeInnerProduct.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeInnerProduct.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeInterp.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeInterp.cpp.o: ../source/shape/ShapeInterp.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_73) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeInterp.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeInterp.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeInterp.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeInterp.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeInterp.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeInterp.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeInterp.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeInterp.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeInterp.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeInterp.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeInterp.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeLSTM.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeLSTM.cpp.o: ../source/shape/ShapeLSTM.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_74) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeLSTM.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeLSTM.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeLSTM.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeLSTM.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeLSTM.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeLSTM.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeLSTM.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeLSTM.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeLSTM.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeLSTM.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeLSTM.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeLinSpace.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeLinSpace.cpp.o: ../source/shape/ShapeLinSpace.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_75) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeLinSpace.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeLinSpace.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeLinSpace.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeLinSpace.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeLinSpace.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeLinSpace.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeLinSpace.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeLinSpace.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeLinSpace.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeLinSpace.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeLinSpace.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeMatMul.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeMatMul.cpp.o: ../source/shape/ShapeMatMul.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_76) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeMatMul.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeMatMul.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeMatMul.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeMatMul.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeMatMul.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeMatMul.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeMatMul.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeMatMul.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeMatMul.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeMatMul.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeMatMul.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeMoments.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeMoments.cpp.o: ../source/shape/ShapeMoments.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_77) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeMoments.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeMoments.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeMoments.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeMoments.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeMoments.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeMoments.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeMoments.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeMoments.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeMoments.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeMoments.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeMoments.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeNonMaxSuppressionV2.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeNonMaxSuppressionV2.cpp.o: ../source/shape/ShapeNonMaxSuppressionV2.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_78) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeNonMaxSuppressionV2.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeNonMaxSuppressionV2.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeNonMaxSuppressionV2.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeNonMaxSuppressionV2.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeNonMaxSuppressionV2.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeNonMaxSuppressionV2.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeNonMaxSuppressionV2.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeNonMaxSuppressionV2.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeNonMaxSuppressionV2.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeNonMaxSuppressionV2.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeNonMaxSuppressionV2.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeOneHot.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeOneHot.cpp.o: ../source/shape/ShapeOneHot.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_79) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeOneHot.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeOneHot.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeOneHot.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeOneHot.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeOneHot.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeOneHot.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeOneHot.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeOneHot.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeOneHot.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeOneHot.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeOneHot.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapePack.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapePack.cpp.o: ../source/shape/ShapePack.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_80) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapePack.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapePack.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapePack.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapePack.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapePack.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapePack.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapePack.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapePack.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapePack.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapePack.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapePack.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapePadding.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapePadding.cpp.o: ../source/shape/ShapePadding.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_81) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapePadding.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapePadding.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapePadding.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapePadding.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapePadding.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapePadding.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapePadding.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapePadding.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapePadding.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapePadding.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapePadding.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapePermute.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapePermute.cpp.o: ../source/shape/ShapePermute.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_82) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapePermute.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapePermute.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapePermute.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapePermute.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapePermute.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapePermute.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapePermute.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapePermute.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapePermute.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapePermute.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapePermute.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapePlugin.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapePlugin.cpp.o: ../source/shape/ShapePlugin.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_83) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapePlugin.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapePlugin.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapePlugin.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapePlugin.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapePlugin.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapePlugin.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapePlugin.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapePlugin.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapePlugin.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapePlugin.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapePlugin.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapePool.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapePool.cpp.o: ../source/shape/ShapePool.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_84) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapePool.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapePool.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapePool.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapePool.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapePool.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapePool.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapePool.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapePool.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapePool.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapePool.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapePool.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapePool3D.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapePool3D.cpp.o: ../source/shape/ShapePool3D.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_85) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapePool3D.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapePool3D.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapePool3D.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapePool3D.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapePool3D.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapePool3D.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapePool3D.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapePool3D.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapePool3D.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapePool3D.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapePool3D.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapePriorbox.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapePriorbox.cpp.o: ../source/shape/ShapePriorbox.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_86) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapePriorbox.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapePriorbox.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapePriorbox.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapePriorbox.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapePriorbox.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapePriorbox.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapePriorbox.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapePriorbox.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapePriorbox.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapePriorbox.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapePriorbox.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeProposal.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeProposal.cpp.o: ../source/shape/ShapeProposal.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_87) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeProposal.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeProposal.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeProposal.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeProposal.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeProposal.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeProposal.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeProposal.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeProposal.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeProposal.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeProposal.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeProposal.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedAvgPool.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedAvgPool.cpp.o: ../source/shape/ShapeQuantizedAvgPool.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_88) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedAvgPool.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedAvgPool.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeQuantizedAvgPool.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedAvgPool.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedAvgPool.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeQuantizedAvgPool.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedAvgPool.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedAvgPool.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedAvgPool.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeQuantizedAvgPool.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedAvgPool.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedMaxPool.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedMaxPool.cpp.o: ../source/shape/ShapeQuantizedMaxPool.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_89) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedMaxPool.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedMaxPool.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeQuantizedMaxPool.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedMaxPool.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedMaxPool.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeQuantizedMaxPool.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedMaxPool.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedMaxPool.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedMaxPool.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeQuantizedMaxPool.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedMaxPool.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeRNNSequenceGRU.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeRNNSequenceGRU.cpp.o: ../source/shape/ShapeRNNSequenceGRU.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_90) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeRNNSequenceGRU.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeRNNSequenceGRU.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeRNNSequenceGRU.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeRNNSequenceGRU.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeRNNSequenceGRU.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeRNNSequenceGRU.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeRNNSequenceGRU.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeRNNSequenceGRU.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeRNNSequenceGRU.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeRNNSequenceGRU.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeRNNSequenceGRU.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeROIAlign.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIAlign.cpp.o: ../source/shape/ShapeROIAlign.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_91) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeROIAlign.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeROIAlign.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeROIAlign.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeROIAlign.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeROIAlign.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeROIAlign.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeROIAlign.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeROIAlign.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeROIAlign.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeROIAlign.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeROIAlign.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeROIPooling.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIPooling.cpp.o: ../source/shape/ShapeROIPooling.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_92) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeROIPooling.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeROIPooling.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeROIPooling.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeROIPooling.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeROIPooling.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeROIPooling.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeROIPooling.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeROIPooling.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeROIPooling.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeROIPooling.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeROIPooling.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeRandomUniform.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeRandomUniform.cpp.o: ../source/shape/ShapeRandomUniform.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_93) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeRandomUniform.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeRandomUniform.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeRandomUniform.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeRandomUniform.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeRandomUniform.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeRandomUniform.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeRandomUniform.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeRandomUniform.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeRandomUniform.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeRandomUniform.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeRandomUniform.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeRange.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeRange.cpp.o: ../source/shape/ShapeRange.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_94) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeRange.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeRange.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeRange.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeRange.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeRange.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeRange.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeRange.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeRange.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeRange.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeRange.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeRange.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeReduction.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeReduction.cpp.o: ../source/shape/ShapeReduction.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_95) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeReduction.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeReduction.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeReduction.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeReduction.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeReduction.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeReduction.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeReduction.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeReduction.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeReduction.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeReduction.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeReduction.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeRegister.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeRegister.cpp.o: ../source/shape/ShapeRegister.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_96) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeRegister.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeRegister.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeRegister.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeRegister.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeRegister.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeRegister.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeRegister.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeRegister.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeRegister.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeRegister.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeRegister.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeReshape.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeReshape.cpp.o: ../source/shape/ShapeReshape.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_97) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeReshape.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeReshape.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeReshape.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeReshape.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeReshape.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeReshape.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeReshape.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeReshape.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeReshape.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeReshape.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeReshape.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeResize.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeResize.cpp.o: ../source/shape/ShapeResize.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_98) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeResize.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeResize.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeResize.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeResize.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeResize.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeResize.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeResize.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeResize.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeResize.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeResize.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeResize.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeScatterNd.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeScatterNd.cpp.o: ../source/shape/ShapeScatterNd.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_99) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeScatterNd.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeScatterNd.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeScatterNd.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeScatterNd.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeScatterNd.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeScatterNd.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeScatterNd.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeScatterNd.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeScatterNd.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeScatterNd.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeScatterNd.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeSegmentMean.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeSegmentMean.cpp.o: ../source/shape/ShapeSegmentMean.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_100) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeSegmentMean.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeSegmentMean.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSegmentMean.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeSegmentMean.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeSegmentMean.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSegmentMean.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeSegmentMean.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeSegmentMean.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeSegmentMean.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSegmentMean.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeSegmentMean.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeSelect.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeSelect.cpp.o: ../source/shape/ShapeSelect.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_101) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeSelect.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeSelect.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSelect.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeSelect.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeSelect.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSelect.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeSelect.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeSelect.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeSelect.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSelect.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeSelect.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeSetDiff1D.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeSetDiff1D.cpp.o: ../source/shape/ShapeSetDiff1D.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_102) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeSetDiff1D.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeSetDiff1D.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSetDiff1D.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeSetDiff1D.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeSetDiff1D.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSetDiff1D.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeSetDiff1D.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeSetDiff1D.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeSetDiff1D.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSetDiff1D.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeSetDiff1D.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeShape.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeShape.cpp.o: ../source/shape/ShapeShape.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_103) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeShape.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeShape.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeShape.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeShape.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeShape.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeShape.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeShape.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeShape.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeShape.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeShape.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeShape.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeSize.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeSize.cpp.o: ../source/shape/ShapeSize.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_104) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeSize.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeSize.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSize.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeSize.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeSize.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSize.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeSize.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeSize.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeSize.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSize.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeSize.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeSlice.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeSlice.cpp.o: ../source/shape/ShapeSlice.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_105) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeSlice.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeSlice.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSlice.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeSlice.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeSlice.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSlice.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeSlice.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeSlice.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeSlice.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSlice.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeSlice.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeSliceTf.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeSliceTf.cpp.o: ../source/shape/ShapeSliceTf.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_106) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeSliceTf.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeSliceTf.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSliceTf.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeSliceTf.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeSliceTf.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSliceTf.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeSliceTf.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeSliceTf.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeSliceTf.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSliceTf.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeSliceTf.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToBatchND.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToBatchND.cpp.o: ../source/shape/ShapeSpaceToBatchND.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_107) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToBatchND.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToBatchND.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSpaceToBatchND.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToBatchND.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToBatchND.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSpaceToBatchND.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToBatchND.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToBatchND.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToBatchND.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSpaceToBatchND.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToBatchND.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToDepth.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToDepth.cpp.o: ../source/shape/ShapeSpaceToDepth.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_108) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToDepth.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToDepth.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSpaceToDepth.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToDepth.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToDepth.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSpaceToDepth.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToDepth.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToDepth.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToDepth.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSpaceToDepth.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToDepth.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeSplitGelu.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeSplitGelu.cpp.o: ../source/shape/ShapeSplitGelu.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_109) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeSplitGelu.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeSplitGelu.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSplitGelu.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeSplitGelu.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeSplitGelu.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSplitGelu.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeSplitGelu.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeSplitGelu.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeSplitGelu.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSplitGelu.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeSplitGelu.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeSqueeze.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeSqueeze.cpp.o: ../source/shape/ShapeSqueeze.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_110) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeSqueeze.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeSqueeze.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSqueeze.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeSqueeze.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeSqueeze.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSqueeze.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeSqueeze.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeSqueeze.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeSqueeze.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSqueeze.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeSqueeze.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeStft.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeStft.cpp.o: ../source/shape/ShapeStft.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_111) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeStft.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeStft.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeStft.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeStft.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeStft.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeStft.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeStft.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeStft.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeStft.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeStft.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeStft.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeStridedSlice.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeStridedSlice.cpp.o: ../source/shape/ShapeStridedSlice.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_112) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeStridedSlice.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeStridedSlice.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeStridedSlice.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeStridedSlice.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeStridedSlice.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeStridedSlice.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeStridedSlice.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeStridedSlice.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeStridedSlice.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeStridedSlice.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeStridedSlice.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeSvd.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeSvd.cpp.o: ../source/shape/ShapeSvd.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_113) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeSvd.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeSvd.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSvd.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeSvd.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeSvd.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSvd.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeSvd.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeSvd.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeSvd.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeSvd.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeSvd.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorArray.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorArray.cpp.o: ../source/shape/ShapeTensorArray.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_114) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorArray.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorArray.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeTensorArray.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorArray.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorArray.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeTensorArray.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorArray.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorArray.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorArray.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeTensorArray.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorArray.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorConvert.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorConvert.cpp.o: ../source/shape/ShapeTensorConvert.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_115) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorConvert.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorConvert.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeTensorConvert.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorConvert.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorConvert.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeTensorConvert.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorConvert.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorConvert.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorConvert.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeTensorConvert.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorConvert.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeTile.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeTile.cpp.o: ../source/shape/ShapeTile.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_116) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeTile.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeTile.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeTile.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeTile.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeTile.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeTile.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeTile.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeTile.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeTile.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeTile.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeTile.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeTopKV2.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeTopKV2.cpp.o: ../source/shape/ShapeTopKV2.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_117) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeTopKV2.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeTopKV2.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeTopKV2.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeTopKV2.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeTopKV2.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeTopKV2.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeTopKV2.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeTopKV2.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeTopKV2.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeTopKV2.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeTopKV2.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeTranspose.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeTranspose.cpp.o: ../source/shape/ShapeTranspose.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_118) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeTranspose.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeTranspose.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeTranspose.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeTranspose.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeTranspose.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeTranspose.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeTranspose.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeTranspose.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeTranspose.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeTranspose.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeTranspose.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeUnique.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnique.cpp.o: ../source/shape/ShapeUnique.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_119) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeUnique.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeUnique.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeUnique.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeUnique.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeUnique.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeUnique.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeUnique.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeUnique.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeUnique.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeUnique.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeUnique.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeUnpack.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnpack.cpp.o: ../source/shape/ShapeUnpack.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_120) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeUnpack.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeUnpack.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeUnpack.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeUnpack.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeUnpack.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeUnpack.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeUnpack.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeUnpack.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeUnpack.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeUnpack.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeUnpack.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeUnravelIndex.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnravelIndex.cpp.o: ../source/shape/ShapeUnravelIndex.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_121) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeUnravelIndex.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeUnravelIndex.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeUnravelIndex.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeUnravelIndex.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeUnravelIndex.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeUnravelIndex.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeUnravelIndex.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeUnravelIndex.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeUnravelIndex.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeUnravelIndex.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeUnravelIndex.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/ShapeWhere.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/ShapeWhere.cpp.o: ../source/shape/ShapeWhere.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_122) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/ShapeWhere.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/ShapeWhere.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeWhere.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeWhere.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/ShapeWhere.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeWhere.cpp > CMakeFiles/MNNTransform.dir/source/shape/ShapeWhere.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/ShapeWhere.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/ShapeWhere.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/ShapeWhere.cpp -o CMakeFiles/MNNTransform.dir/source/shape/ShapeWhere.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/SizeComputer.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/SizeComputer.cpp.o: ../source/shape/SizeComputer.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_123) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/SizeComputer.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/SizeComputer.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/SizeComputer.cpp

CMakeFiles/MNNTransform.dir/source/shape/SizeComputer.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/SizeComputer.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/SizeComputer.cpp > CMakeFiles/MNNTransform.dir/source/shape/SizeComputer.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/SizeComputer.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/SizeComputer.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/SizeComputer.cpp -o CMakeFiles/MNNTransform.dir/source/shape/SizeComputer.cpp.s

CMakeFiles/MNNTransform.dir/source/shape/render/ShapeRasterAndInterpolate.cpp.o: CMakeFiles/MNNTransform.dir/flags.make
CMakeFiles/MNNTransform.dir/source/shape/render/ShapeRasterAndInterpolate.cpp.o: ../source/shape/render/ShapeRasterAndInterpolate.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_124) "Building CXX object CMakeFiles/MNNTransform.dir/source/shape/render/ShapeRasterAndInterpolate.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNTransform.dir/source/shape/render/ShapeRasterAndInterpolate.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/render/ShapeRasterAndInterpolate.cpp

CMakeFiles/MNNTransform.dir/source/shape/render/ShapeRasterAndInterpolate.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNTransform.dir/source/shape/render/ShapeRasterAndInterpolate.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/render/ShapeRasterAndInterpolate.cpp > CMakeFiles/MNNTransform.dir/source/shape/render/ShapeRasterAndInterpolate.cpp.i

CMakeFiles/MNNTransform.dir/source/shape/render/ShapeRasterAndInterpolate.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNTransform.dir/source/shape/render/ShapeRasterAndInterpolate.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/shape/render/ShapeRasterAndInterpolate.cpp -o CMakeFiles/MNNTransform.dir/source/shape/render/ShapeRasterAndInterpolate.cpp.s

MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/ConvertUtils.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometryBroadcastTo.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometryConcat.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometryCrop.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometryDepthToSpace.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometryDet.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometryFill.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometryLSTM.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometryOPRegister.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometryPermute.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometryReshape.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometryReverseSequence.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometrySelect.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometrySlice.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpaceToBatchND.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometryStridedSlice.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometryTile.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeArgMax.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeAttention.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeBatchToSpaceND.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeBinaryOp.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeBroadcastTo.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeCast.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeConcat.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeConvTranspose3D.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution3D.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeCosineSimilarity.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeCrop.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeCropAndResize.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeDeconvolution.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeDepthToSpace.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeDequantize.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeDet.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionOutput.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionPostProcess.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeDynamicQuant.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeExpandDims.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeFill.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherND.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherV2.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeGridSample.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeHistogram.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeInnerProduct.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeInterp.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeLSTM.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeLinSpace.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeMatMul.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeMoments.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeNonMaxSuppressionV2.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeOneHot.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapePack.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapePadding.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapePermute.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapePlugin.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapePool.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapePool3D.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapePriorbox.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeProposal.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedAvgPool.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedMaxPool.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeRNNSequenceGRU.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeROIAlign.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeROIPooling.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeRandomUniform.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeRange.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeReduction.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeRegister.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeReshape.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeResize.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeScatterNd.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeSegmentMean.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeSelect.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeSetDiff1D.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeShape.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeSize.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeSlice.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeSliceTf.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToBatchND.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToDepth.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeSplitGelu.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeSqueeze.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeStft.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeStridedSlice.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeSvd.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorArray.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorConvert.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeTile.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeTopKV2.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeTranspose.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeUnique.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeUnpack.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeUnravelIndex.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/ShapeWhere.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/SizeComputer.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/source/shape/render/ShapeRasterAndInterpolate.cpp.o
MNNTransform: CMakeFiles/MNNTransform.dir/build.make

.PHONY : MNNTransform

# Rule to build all files generated by this target.
CMakeFiles/MNNTransform.dir/build: MNNTransform

.PHONY : CMakeFiles/MNNTransform.dir/build

CMakeFiles/MNNTransform.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/MNNTransform.dir/cmake_clean.cmake
.PHONY : CMakeFiles/MNNTransform.dir/clean

CMakeFiles/MNNTransform.dir/depend:
	cd /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /root/pan/shunzao_ai_lib-develop/MNN-master /root/pan/shunzao_ai_lib-develop/MNN-master /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNTransform.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/MNNTransform.dir/depend

