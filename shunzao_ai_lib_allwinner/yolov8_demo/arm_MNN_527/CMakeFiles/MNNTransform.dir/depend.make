# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

CMakeFiles/MNNTransform.dir/source/geometry/ConvertUtils.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/ConvertUtils.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/ConvertUtils.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/ConvertUtils.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/ConvertUtils.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/ConvertUtils.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/ConvertUtils.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/ConvertUtils.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/ConvertUtils.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/ConvertUtils.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/ConvertUtils.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/ConvertUtils.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/ConvertUtils.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/ConvertUtils.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/ConvertUtils.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/ConvertUtils.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/ConvertUtils.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/ConvertUtils.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/ConvertUtils.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/ConvertUtils.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/ConvertUtils.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNNTransform.dir/source/geometry/ConvertUtils.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/ConvertUtils.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/ConvertUtils.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/ConvertUtils.cpp.o: ../source/core/OpCommonUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/ConvertUtils.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/ConvertUtils.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/ConvertUtils.cpp.o: ../source/geometry/ConvertUtils.cpp
CMakeFiles/MNNTransform.dir/source/geometry/ConvertUtils.cpp.o: ../source/geometry/ConvertUtils.hpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.o: ../include/MNN/Interpreter.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.o: ../source/core/OpCommonUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.o: ../source/core/Schedule.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.o: ../source/geometry/GeometryComputerUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBatchMatMul.cpp.o: ../source/geometry/GeometryBatchMatMul.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.o: ../include/MNN/Interpreter.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.o: ../source/core/Schedule.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.o: ../source/geometry/GeometryComputerUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.o: ../source/geometry/ConvertUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBinary.cpp.o: ../source/geometry/GeometryBinary.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryBroadcastTo.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBroadcastTo.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBroadcastTo.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBroadcastTo.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBroadcastTo.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBroadcastTo.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBroadcastTo.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBroadcastTo.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBroadcastTo.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBroadcastTo.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBroadcastTo.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBroadcastTo.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBroadcastTo.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBroadcastTo.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBroadcastTo.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBroadcastTo.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBroadcastTo.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBroadcastTo.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBroadcastTo.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBroadcastTo.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBroadcastTo.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBroadcastTo.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBroadcastTo.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBroadcastTo.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBroadcastTo.cpp.o: ../source/core/OpCommonUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBroadcastTo.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBroadcastTo.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBroadcastTo.cpp.o: ../source/geometry/ConvertUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryBroadcastTo.cpp.o: ../source/geometry/GeometryBroadcastTo.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.o: ../include/MNN/Interpreter.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.o: ../source/core/OpCommonUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputer.cpp.o: ../source/geometry/GeometryComputer.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.o: ../codegen/OpFuse.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.o: ../include/MNN/Interpreter.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.o: ../source/core/OpCommonUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.o: ../source/core/RuntimeFactory.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.o: ../source/core/Schedule.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.o: ../source/geometry/GeometryComputerUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.o: ../source/geometry/GeometryComputerUtils.cpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryComputerUtils.cpp.o: ../source/geometry/GeometryComputerUtils.hpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryConcat.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConcat.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConcat.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConcat.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConcat.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConcat.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConcat.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConcat.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConcat.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConcat.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConcat.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConcat.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConcat.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConcat.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConcat.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConcat.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConcat.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConcat.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConcat.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConcat.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConcat.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConcat.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConcat.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConcat.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConcat.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConcat.cpp.o: ../source/geometry/GeometryConcat.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.o: ../include/MNN/AutoTime.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.o: ../include/MNN/Interpreter.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.o: ../source/core/ConvolutionCommon.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.o: ../source/core/OpCommonUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.o: ../source/core/Schedule.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.o: ../source/geometry/GeometryComputerUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.o: ../source/geometry/ConvertUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.o: ../source/geometry/GeometryConv2D.cpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2D.cpp.o: ../source/geometry/GeometryConvUtils.hpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.o: ../include/MNN/AutoTime.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.o: ../include/MNN/Interpreter.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.o: ../source/core/ConvolutionCommon.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.o: ../source/core/OpCommonUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.o: ../source/core/Schedule.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.o: ../source/geometry/GeometryComputerUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.o: ../source/geometry/ConvertUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.o: ../source/geometry/GeometryConv2DBackPropFilter.cpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv2DBackPropFilter.cpp.o: ../source/geometry/GeometryConvUtils.hpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.o: ../include/MNN/Interpreter.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.o: ../source/core/ConvolutionCommon.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.o: ../source/core/OpCommonUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.o: ../source/core/Schedule.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.o: ../source/geometry/GeometryComputerUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.o: ../source/geometry/ConvertUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.o: ../source/geometry/GeometryConv3D.cpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConv3D.cpp.o: ../source/geometry/GeometryConvUtils.hpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.o: ../include/MNN/AutoTime.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.o: ../include/MNN/Interpreter.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.o: ../source/core/ConvolutionCommon.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.o: ../source/core/OpCommonUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.o: ../source/core/Schedule.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.o: ../source/geometry/GeometryComputerUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.o: ../source/geometry/ConvertUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.o: ../source/geometry/GeometryConvUtils.cpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvUtils.cpp.o: ../source/geometry/GeometryConvUtils.hpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.o: ../include/MNN/Interpreter.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.o: ../source/core/OpCommonUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.o: ../source/core/Schedule.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.o: ../source/geometry/ConvertUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.o: ../source/geometry/GeometryComputerUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryConvert.cpp.o: ../source/geometry/GeometryConvert.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.o: ../include/MNN/Interpreter.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.o: ../source/core/OpCommonUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.o: ../source/core/Schedule.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.o: ../source/geometry/GeometryComputerUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCosineSimilarity.cpp.o: ../source/geometry/GeometryCosineSimilarity.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryCrop.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCrop.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCrop.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCrop.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCrop.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCrop.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCrop.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCrop.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCrop.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCrop.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCrop.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCrop.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCrop.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCrop.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCrop.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCrop.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCrop.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCrop.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCrop.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCrop.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCrop.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCrop.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCrop.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCrop.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCrop.cpp.o: ../source/core/OpCommonUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCrop.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCrop.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCrop.cpp.o: ../source/geometry/GeometryCrop.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.o: ../include/MNN/Interpreter.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.o: ../source/core/OpCommonUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.o: ../source/core/Schedule.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.o: ../source/geometry/GeometryComputerUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryCumSum.cpp.o: ../source/geometry/GeometryCumSum.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryDepthToSpace.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDepthToSpace.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDepthToSpace.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDepthToSpace.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDepthToSpace.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDepthToSpace.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDepthToSpace.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDepthToSpace.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDepthToSpace.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDepthToSpace.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDepthToSpace.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDepthToSpace.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDepthToSpace.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDepthToSpace.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDepthToSpace.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDepthToSpace.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDepthToSpace.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDepthToSpace.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDepthToSpace.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDepthToSpace.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDepthToSpace.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDepthToSpace.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDepthToSpace.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDepthToSpace.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDepthToSpace.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDepthToSpace.cpp.o: ../source/geometry/GeometryDepthToSpace.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryDet.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDet.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDet.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDet.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDet.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDet.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDet.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDet.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDet.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDet.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDet.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDet.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDet.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDet.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDet.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDet.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDet.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDet.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDet.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDet.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDet.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDet.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDet.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDet.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDet.cpp.o: ../source/core/OpCommonUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDet.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDet.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDet.cpp.o: ../source/geometry/GeometryDet.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.o: ../include/MNN/Interpreter.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.o: ../source/core/ConvolutionCommon.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.o: ../source/core/OpCommonUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.o: ../source/core/Schedule.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.o: ../source/geometry/GeometryComputerUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.o: ../source/geometry/ConvertUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.o: ../source/geometry/GeometryConvUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryDilation2D.cpp.o: ../source/geometry/GeometryDilation2D.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.o: ../include/MNN/Interpreter.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.o: ../source/core/OpCommonUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.o: ../source/core/Schedule.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.o: ../source/geometry/GeometryComputerUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryELU.cpp.o: ../source/geometry/GeometryELU.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryFill.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryFill.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryFill.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryFill.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryFill.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryFill.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryFill.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryFill.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryFill.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryFill.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryFill.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryFill.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryFill.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryFill.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryFill.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryFill.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryFill.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryFill.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryFill.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryFill.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryFill.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryFill.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryFill.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryFill.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryFill.cpp.o: ../source/core/OpCommonUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryFill.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryFill.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryFill.cpp.o: ../source/geometry/GeometryFill.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.o: ../include/MNN/Interpreter.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.o: ../source/core/OpCommonUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.o: ../source/core/Schedule.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.o: ../source/geometry/GeometryComputerUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryGather.cpp.o: ../source/geometry/GeometryGather.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.o: ../include/MNN/Interpreter.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.o: ../source/core/Schedule.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.o: ../source/geometry/GeometryComputerUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.o: ../source/geometry/ConvertUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryImageOp.cpp.o: ../source/geometry/GeometryImageOp.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.o: ../include/MNN/AutoTime.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.o: ../include/MNN/Interpreter.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.o: ../source/core/ConvolutionCommon.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.o: ../source/core/OpCommonUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.o: ../source/core/Schedule.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.o: ../source/geometry/GeometryComputerUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.o: ../source/geometry/ConvertUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryInnerProduct.cpp.o: ../source/geometry/GeometryInnerProduct.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.o: ../include/MNN/AutoTime.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.o: ../include/MNN/Interpreter.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.o: ../source/core/OpCommonUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.o: ../source/core/Schedule.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.o: ../source/geometry/GeometryComputerUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.o: ../source/geometry/ConvertUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLRN.cpp.o: ../source/geometry/GeometryLRN.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryLSTM.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLSTM.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLSTM.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLSTM.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLSTM.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLSTM.cpp.o: ../include/MNN/Interpreter.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLSTM.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLSTM.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLSTM.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLSTM.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLSTM.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLSTM.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLSTM.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLSTM.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLSTM.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLSTM.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLSTM.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLSTM.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLSTM.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLSTM.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLSTM.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLSTM.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLSTM.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLSTM.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLSTM.cpp.o: ../source/core/Schedule.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLSTM.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLSTM.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLSTM.cpp.o: ../source/geometry/GeometryComputerUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLSTM.cpp.o: ../source/geometry/GeometryLSTM.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.o: ../include/MNN/Interpreter.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.o: ../source/core/OpCommonUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.o: ../source/core/Schedule.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.o: ../source/geometry/GeometryComputerUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryLayernorm.cpp.o: ../source/geometry/GeometryLayernorm.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryOPRegister.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryOPRegister.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryOPRegister.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryOPRegister.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryOPRegister.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryOPRegister.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryOPRegister.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryOPRegister.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryOPRegister.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryOPRegister.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryOPRegister.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryOPRegister.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryOPRegister.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryOPRegister.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryOPRegister.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryOPRegister.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryOPRegister.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryOPRegister.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryOPRegister.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryOPRegister.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryOPRegister.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryOPRegister.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryOPRegister.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryOPRegister.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryOPRegister.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryOPRegister.cpp.o: ../source/geometry/GeometryOPRegister.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryPermute.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPermute.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPermute.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPermute.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPermute.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPermute.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPermute.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPermute.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPermute.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPermute.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPermute.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPermute.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPermute.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPermute.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPermute.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPermute.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPermute.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPermute.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPermute.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPermute.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPermute.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPermute.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPermute.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPermute.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPermute.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPermute.cpp.o: ../source/geometry/GeometryPermute.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.o: ../include/MNN/AutoTime.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.o: ../include/MNN/Interpreter.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.o: ../source/core/OpCommonUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.o: ../source/core/Schedule.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.o: ../source/geometry/GeometryComputerUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.o: ../source/geometry/ConvertUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPoolGrad.cpp.o: ../source/geometry/GeometryPoolGrad.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.o: ../include/MNN/Interpreter.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.o: ../source/core/OpCommonUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.o: ../source/core/Schedule.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.o: ../source/geometry/GeometryComputerUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.o: ../source/geometry/ConvertUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryPooling3D.cpp.o: ../source/geometry/GeometryPooling3D.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.o: ../include/MNN/Interpreter.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.o: ../source/core/OpCommonUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.o: ../source/core/Schedule.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.o: ../source/geometry/GeometryComputerUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReduce.cpp.o: ../source/geometry/GeometryReduce.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryReshape.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReshape.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReshape.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReshape.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReshape.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReshape.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReshape.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReshape.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReshape.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReshape.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReshape.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReshape.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReshape.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReshape.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReshape.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReshape.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReshape.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReshape.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReshape.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReshape.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReshape.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReshape.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReshape.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReshape.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReshape.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReshape.cpp.o: ../source/geometry/ConvertUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReshape.cpp.o: ../source/geometry/GeometryReshape.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryReverseSequence.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReverseSequence.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReverseSequence.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReverseSequence.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReverseSequence.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReverseSequence.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReverseSequence.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReverseSequence.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReverseSequence.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReverseSequence.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReverseSequence.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReverseSequence.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReverseSequence.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReverseSequence.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReverseSequence.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReverseSequence.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReverseSequence.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReverseSequence.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReverseSequence.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReverseSequence.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReverseSequence.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReverseSequence.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReverseSequence.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReverseSequence.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReverseSequence.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryReverseSequence.cpp.o: ../source/geometry/GeometryReverseSequence.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.o: ../include/MNN/Interpreter.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.o: ../source/core/OpCommonUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.o: ../source/core/Schedule.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.o: ../source/geometry/GeometryComputerUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryScatter.cpp.o: ../source/geometry/GeometryScatter.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometrySelect.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySelect.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySelect.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySelect.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySelect.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySelect.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySelect.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySelect.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySelect.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySelect.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySelect.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySelect.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySelect.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySelect.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySelect.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySelect.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySelect.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySelect.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySelect.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySelect.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySelect.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySelect.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySelect.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySelect.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySelect.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySelect.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySelect.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySelect.cpp.o: ../source/geometry/ConvertUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySelect.cpp.o: ../source/geometry/GeometrySelect.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.o: ../include/MNN/Interpreter.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.o: ../include/MNN/Rect.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.o: ../source/backend/cpu/compute/CommonOptFunction.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.o: ../source/backend/cpu/compute/Int8FunctionsOpt.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.o: ../source/core/ConvolutionCommon.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.o: ../source/core/Schedule.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.o: ../source/geometry/GeometryComputerUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryShape.cpp.o: ../source/geometry/GeometryShape.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometrySlice.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySlice.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySlice.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySlice.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySlice.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySlice.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySlice.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySlice.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySlice.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySlice.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySlice.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySlice.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySlice.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySlice.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySlice.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySlice.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySlice.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySlice.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySlice.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySlice.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySlice.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySlice.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySlice.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySlice.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySlice.cpp.o: ../source/core/OpCommonUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySlice.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySlice.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySlice.cpp.o: ../source/geometry/GeometrySlice.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpaceToBatchND.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpaceToBatchND.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpaceToBatchND.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpaceToBatchND.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpaceToBatchND.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpaceToBatchND.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpaceToBatchND.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpaceToBatchND.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpaceToBatchND.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpaceToBatchND.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpaceToBatchND.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpaceToBatchND.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpaceToBatchND.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpaceToBatchND.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpaceToBatchND.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpaceToBatchND.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpaceToBatchND.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpaceToBatchND.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpaceToBatchND.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpaceToBatchND.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpaceToBatchND.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpaceToBatchND.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpaceToBatchND.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpaceToBatchND.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpaceToBatchND.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpaceToBatchND.cpp.o: ../source/geometry/GeometrySpaceToBatchND.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.o: ../include/MNN/Interpreter.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.o: ../source/core/OpCommonUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.o: ../source/core/Schedule.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.o: ../source/geometry/GeometryComputerUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometrySpatialProduct.cpp.o: ../source/geometry/GeometrySpatialProduct.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryStridedSlice.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryStridedSlice.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryStridedSlice.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryStridedSlice.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryStridedSlice.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryStridedSlice.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryStridedSlice.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryStridedSlice.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryStridedSlice.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryStridedSlice.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryStridedSlice.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryStridedSlice.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryStridedSlice.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryStridedSlice.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryStridedSlice.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryStridedSlice.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryStridedSlice.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryStridedSlice.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryStridedSlice.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryStridedSlice.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryStridedSlice.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryStridedSlice.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryStridedSlice.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryStridedSlice.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryStridedSlice.cpp.o: ../source/core/OpCommonUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryStridedSlice.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryStridedSlice.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryStridedSlice.cpp.o: ../source/geometry/ConvertUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryStridedSlice.cpp.o: ../source/geometry/GeometryStridedSlice.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.o: ../include/MNN/Interpreter.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.o: ../source/core/OpCommonUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.o: ../source/core/Schedule.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.o: ../source/geometry/GeometryComputerUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTensorArray.cpp.o: ../source/geometry/GeometryTensorArray.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.o: ../include/MNN/Interpreter.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.o: ../source/core/OpCommonUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.o: ../source/core/Schedule.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.o: ../source/geometry/GeometryComputerUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryThreshold.cpp.o: ../source/geometry/GeometryThreshold.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryTile.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTile.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTile.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTile.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTile.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTile.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTile.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTile.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTile.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTile.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTile.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTile.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTile.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTile.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTile.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTile.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTile.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTile.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTile.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTile.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTile.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTile.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTile.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTile.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTile.cpp.o: ../source/core/OpCommonUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTile.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTile.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTile.cpp.o: ../source/geometry/GeometryTile.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.o: ../include/MNN/Interpreter.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.o: ../source/core/OpCommonUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.o: ../source/core/Schedule.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.o: ../source/geometry/GeometryComputerUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryTopK.cpp.o: ../source/geometry/GeometryTopK.cpp

CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.o: ../include/MNN/Interpreter.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.o: ../source/core/OpCommonUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.o: ../source/core/Schedule.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.o: ../source/geometry/GeometryComputer.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.o: ../source/geometry/GeometryComputerUtils.hpp
CMakeFiles/MNNTransform.dir/source/geometry/GeometryUnary.cpp.o: ../source/geometry/GeometryUnary.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeArgMax.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeArgMax.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeArgMax.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeArgMax.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeArgMax.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeArgMax.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeArgMax.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeArgMax.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeArgMax.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeArgMax.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeArgMax.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeArgMax.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeArgMax.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeArgMax.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeArgMax.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeArgMax.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeArgMax.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeArgMax.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeArgMax.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeArgMax.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeArgMax.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeArgMax.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeArgMax.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeArgMax.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeArgMax.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeArgMax.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeArgMax.cpp.o: ../source/shape/ShapeArgMax.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeAttention.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeAttention.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeAttention.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeAttention.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeAttention.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeAttention.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeAttention.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeAttention.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeAttention.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeAttention.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeAttention.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeAttention.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeAttention.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeAttention.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeAttention.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeAttention.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeAttention.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeAttention.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeAttention.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeAttention.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeAttention.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeAttention.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeAttention.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeAttention.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeAttention.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeAttention.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeAttention.cpp.o: ../source/shape/ShapeAttention.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeBatchToSpaceND.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBatchToSpaceND.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBatchToSpaceND.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBatchToSpaceND.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeBatchToSpaceND.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBatchToSpaceND.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBatchToSpaceND.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBatchToSpaceND.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeBatchToSpaceND.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBatchToSpaceND.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBatchToSpaceND.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBatchToSpaceND.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBatchToSpaceND.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBatchToSpaceND.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBatchToSpaceND.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBatchToSpaceND.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBatchToSpaceND.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBatchToSpaceND.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeBatchToSpaceND.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeBatchToSpaceND.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeBatchToSpaceND.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeBatchToSpaceND.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBatchToSpaceND.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBatchToSpaceND.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeBatchToSpaceND.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeBatchToSpaceND.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeBatchToSpaceND.cpp.o: ../source/shape/ShapeBatchToSpaceND.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeBinaryOp.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBinaryOp.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBinaryOp.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBinaryOp.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeBinaryOp.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBinaryOp.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBinaryOp.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBinaryOp.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeBinaryOp.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBinaryOp.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBinaryOp.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBinaryOp.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBinaryOp.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBinaryOp.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBinaryOp.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBinaryOp.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBinaryOp.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBinaryOp.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeBinaryOp.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeBinaryOp.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeBinaryOp.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeBinaryOp.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBinaryOp.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBinaryOp.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeBinaryOp.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeBinaryOp.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeBinaryOp.cpp.o: ../source/shape/ShapeBinaryOp.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeBroadcastTo.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBroadcastTo.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBroadcastTo.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBroadcastTo.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeBroadcastTo.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBroadcastTo.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBroadcastTo.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBroadcastTo.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeBroadcastTo.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBroadcastTo.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBroadcastTo.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBroadcastTo.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBroadcastTo.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBroadcastTo.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBroadcastTo.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBroadcastTo.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBroadcastTo.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBroadcastTo.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeBroadcastTo.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeBroadcastTo.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeBroadcastTo.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeBroadcastTo.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBroadcastTo.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeBroadcastTo.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeBroadcastTo.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeBroadcastTo.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeBroadcastTo.cpp.o: ../source/shape/ShapeBroadcastTo.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeCast.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCast.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCast.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCast.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeCast.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCast.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCast.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCast.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeCast.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCast.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCast.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCast.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCast.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCast.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCast.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCast.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCast.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCast.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeCast.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeCast.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeCast.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeCast.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCast.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCast.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeCast.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeCast.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeCast.cpp.o: ../source/shape/ShapeCast.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeConcat.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConcat.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConcat.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConcat.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeConcat.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConcat.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConcat.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConcat.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeConcat.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConcat.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConcat.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConcat.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConcat.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConcat.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConcat.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConcat.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConcat.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConcat.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeConcat.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeConcat.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeConcat.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeConcat.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConcat.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConcat.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeConcat.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeConcat.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeConcat.cpp.o: ../source/shape/ShapeConcat.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeConvTranspose3D.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvTranspose3D.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvTranspose3D.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvTranspose3D.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvTranspose3D.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvTranspose3D.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvTranspose3D.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvTranspose3D.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvTranspose3D.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvTranspose3D.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvTranspose3D.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvTranspose3D.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvTranspose3D.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvTranspose3D.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvTranspose3D.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvTranspose3D.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvTranspose3D.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvTranspose3D.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvTranspose3D.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvTranspose3D.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvTranspose3D.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvTranspose3D.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvTranspose3D.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvTranspose3D.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvTranspose3D.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvTranspose3D.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvTranspose3D.cpp.o: ../source/shape/ShapeConvTranspose3D.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution.cpp.o: ../source/shape/ShapeConvolution.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution3D.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution3D.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution3D.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution3D.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution3D.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution3D.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution3D.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution3D.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution3D.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution3D.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution3D.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution3D.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution3D.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution3D.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution3D.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution3D.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution3D.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution3D.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution3D.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution3D.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution3D.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution3D.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution3D.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution3D.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution3D.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution3D.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeConvolution3D.cpp.o: ../source/shape/ShapeConvolution3D.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeCosineSimilarity.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCosineSimilarity.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCosineSimilarity.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCosineSimilarity.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeCosineSimilarity.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCosineSimilarity.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCosineSimilarity.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCosineSimilarity.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeCosineSimilarity.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCosineSimilarity.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCosineSimilarity.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCosineSimilarity.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCosineSimilarity.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCosineSimilarity.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCosineSimilarity.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCosineSimilarity.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCosineSimilarity.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCosineSimilarity.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeCosineSimilarity.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeCosineSimilarity.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeCosineSimilarity.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeCosineSimilarity.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCosineSimilarity.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCosineSimilarity.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeCosineSimilarity.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeCosineSimilarity.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeCosineSimilarity.cpp.o: ../source/shape/ShapeCosineSimilarity.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeCrop.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCrop.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCrop.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCrop.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeCrop.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCrop.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCrop.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCrop.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeCrop.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCrop.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCrop.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCrop.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCrop.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCrop.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCrop.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCrop.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCrop.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCrop.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeCrop.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeCrop.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeCrop.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeCrop.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCrop.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCrop.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeCrop.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeCrop.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeCrop.cpp.o: ../source/shape/ShapeCrop.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeCropAndResize.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCropAndResize.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCropAndResize.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCropAndResize.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeCropAndResize.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCropAndResize.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCropAndResize.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCropAndResize.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeCropAndResize.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCropAndResize.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCropAndResize.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCropAndResize.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCropAndResize.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCropAndResize.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCropAndResize.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCropAndResize.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCropAndResize.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCropAndResize.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeCropAndResize.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeCropAndResize.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeCropAndResize.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeCropAndResize.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCropAndResize.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeCropAndResize.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeCropAndResize.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeCropAndResize.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeCropAndResize.cpp.o: ../source/shape/ShapeCropAndResize.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeDeconvolution.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDeconvolution.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDeconvolution.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDeconvolution.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDeconvolution.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDeconvolution.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDeconvolution.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDeconvolution.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDeconvolution.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDeconvolution.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDeconvolution.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDeconvolution.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDeconvolution.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDeconvolution.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDeconvolution.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDeconvolution.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDeconvolution.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDeconvolution.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDeconvolution.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDeconvolution.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDeconvolution.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDeconvolution.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDeconvolution.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDeconvolution.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDeconvolution.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDeconvolution.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDeconvolution.cpp.o: ../source/shape/ShapeDeconvolution.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeDepthToSpace.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDepthToSpace.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDepthToSpace.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDepthToSpace.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDepthToSpace.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDepthToSpace.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDepthToSpace.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDepthToSpace.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDepthToSpace.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDepthToSpace.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDepthToSpace.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDepthToSpace.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDepthToSpace.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDepthToSpace.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDepthToSpace.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDepthToSpace.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDepthToSpace.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDepthToSpace.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDepthToSpace.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDepthToSpace.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDepthToSpace.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDepthToSpace.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDepthToSpace.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDepthToSpace.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDepthToSpace.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDepthToSpace.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDepthToSpace.cpp.o: ../source/shape/ShapeDepthToSpace.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeDequantize.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDequantize.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDequantize.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDequantize.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDequantize.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDequantize.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDequantize.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDequantize.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDequantize.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDequantize.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDequantize.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDequantize.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDequantize.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDequantize.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDequantize.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDequantize.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDequantize.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDequantize.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDequantize.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDequantize.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDequantize.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDequantize.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDequantize.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDequantize.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDequantize.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDequantize.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDequantize.cpp.o: ../source/shape/ShapeDequantize.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeDet.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDet.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDet.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDet.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDet.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDet.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDet.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDet.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDet.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDet.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDet.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDet.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDet.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDet.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDet.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDet.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDet.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDet.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDet.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDet.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDet.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDet.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDet.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDet.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDet.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDet.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDet.cpp.o: ../source/shape/ShapeDet.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionOutput.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionOutput.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionOutput.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionOutput.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionOutput.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionOutput.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionOutput.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionOutput.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionOutput.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionOutput.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionOutput.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionOutput.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionOutput.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionOutput.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionOutput.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionOutput.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionOutput.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionOutput.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionOutput.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionOutput.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionOutput.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionOutput.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionOutput.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionOutput.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionOutput.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionOutput.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionOutput.cpp.o: ../source/shape/ShapeDetectionOutput.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionPostProcess.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionPostProcess.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionPostProcess.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionPostProcess.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionPostProcess.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionPostProcess.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionPostProcess.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionPostProcess.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionPostProcess.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionPostProcess.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionPostProcess.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionPostProcess.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionPostProcess.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionPostProcess.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionPostProcess.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionPostProcess.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionPostProcess.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionPostProcess.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionPostProcess.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionPostProcess.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionPostProcess.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionPostProcess.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionPostProcess.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionPostProcess.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionPostProcess.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionPostProcess.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDetectionPostProcess.cpp.o: ../source/shape/ShapeDetectionPostProcess.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeDynamicQuant.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDynamicQuant.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDynamicQuant.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDynamicQuant.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDynamicQuant.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDynamicQuant.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDynamicQuant.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDynamicQuant.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDynamicQuant.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDynamicQuant.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDynamicQuant.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDynamicQuant.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDynamicQuant.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDynamicQuant.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDynamicQuant.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDynamicQuant.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDynamicQuant.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDynamicQuant.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDynamicQuant.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDynamicQuant.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDynamicQuant.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDynamicQuant.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDynamicQuant.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeDynamicQuant.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDynamicQuant.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDynamicQuant.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeDynamicQuant.cpp.o: ../source/shape/ShapeDynamicQuant.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeExpandDims.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeExpandDims.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeExpandDims.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeExpandDims.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeExpandDims.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeExpandDims.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeExpandDims.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeExpandDims.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeExpandDims.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeExpandDims.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeExpandDims.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeExpandDims.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeExpandDims.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeExpandDims.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeExpandDims.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeExpandDims.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeExpandDims.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeExpandDims.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeExpandDims.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeExpandDims.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeExpandDims.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeExpandDims.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeExpandDims.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeExpandDims.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeExpandDims.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeExpandDims.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeExpandDims.cpp.o: ../source/shape/ShapeExpandDims.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeFill.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeFill.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeFill.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeFill.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeFill.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeFill.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeFill.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeFill.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeFill.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeFill.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeFill.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeFill.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeFill.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeFill.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeFill.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeFill.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeFill.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeFill.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeFill.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeFill.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeFill.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeFill.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeFill.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeFill.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeFill.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeFill.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeFill.cpp.o: ../source/shape/ShapeFill.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherND.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherND.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherND.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherND.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherND.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherND.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherND.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherND.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherND.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherND.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherND.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherND.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherND.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherND.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherND.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherND.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherND.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherND.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherND.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherND.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherND.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherND.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherND.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherND.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherND.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherND.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherND.cpp.o: ../source/shape/ShapeGatherND.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherV2.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherV2.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherV2.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherV2.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherV2.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherV2.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherV2.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherV2.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherV2.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherV2.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherV2.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherV2.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherV2.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherV2.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherV2.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherV2.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherV2.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherV2.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherV2.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherV2.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherV2.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherV2.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherV2.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherV2.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherV2.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherV2.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeGatherV2.cpp.o: ../source/shape/ShapeGatherV2.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeGridSample.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGridSample.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGridSample.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGridSample.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeGridSample.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGridSample.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGridSample.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGridSample.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeGridSample.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGridSample.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGridSample.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGridSample.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGridSample.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGridSample.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGridSample.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGridSample.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGridSample.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGridSample.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeGridSample.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeGridSample.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeGridSample.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeGridSample.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGridSample.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeGridSample.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeGridSample.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeGridSample.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeGridSample.cpp.o: ../source/shape/ShapeGridSample.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeHistogram.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeHistogram.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeHistogram.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeHistogram.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeHistogram.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeHistogram.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeHistogram.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeHistogram.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeHistogram.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeHistogram.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeHistogram.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeHistogram.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeHistogram.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeHistogram.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeHistogram.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeHistogram.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeHistogram.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeHistogram.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeHistogram.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeHistogram.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeHistogram.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeHistogram.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeHistogram.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeHistogram.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeHistogram.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeHistogram.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeHistogram.cpp.o: ../source/shape/ShapeHistogram.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeInnerProduct.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeInnerProduct.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeInnerProduct.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeInnerProduct.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeInnerProduct.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeInnerProduct.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeInnerProduct.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeInnerProduct.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeInnerProduct.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeInnerProduct.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeInnerProduct.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeInnerProduct.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeInnerProduct.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeInnerProduct.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeInnerProduct.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeInnerProduct.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeInnerProduct.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeInnerProduct.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeInnerProduct.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeInnerProduct.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeInnerProduct.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeInnerProduct.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeInnerProduct.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeInnerProduct.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeInnerProduct.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeInnerProduct.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeInnerProduct.cpp.o: ../source/shape/ShapeInnerProduct.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeInterp.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeInterp.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeInterp.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeInterp.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeInterp.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeInterp.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeInterp.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeInterp.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeInterp.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeInterp.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeInterp.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeInterp.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeInterp.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeInterp.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeInterp.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeInterp.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeInterp.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeInterp.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeInterp.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeInterp.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeInterp.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeInterp.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeInterp.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeInterp.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeInterp.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeInterp.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeInterp.cpp.o: ../source/shape/ShapeInterp.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeLSTM.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeLSTM.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeLSTM.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeLSTM.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeLSTM.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeLSTM.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeLSTM.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeLSTM.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeLSTM.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeLSTM.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeLSTM.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeLSTM.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeLSTM.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeLSTM.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeLSTM.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeLSTM.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeLSTM.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeLSTM.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeLSTM.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeLSTM.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeLSTM.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeLSTM.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeLSTM.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeLSTM.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeLSTM.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeLSTM.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeLSTM.cpp.o: ../source/shape/ShapeLSTM.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeLinSpace.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeLinSpace.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeLinSpace.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeLinSpace.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeLinSpace.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeLinSpace.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeLinSpace.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeLinSpace.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeLinSpace.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeLinSpace.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeLinSpace.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeLinSpace.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeLinSpace.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeLinSpace.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeLinSpace.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeLinSpace.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeLinSpace.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeLinSpace.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeLinSpace.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeLinSpace.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeLinSpace.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeLinSpace.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeLinSpace.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeLinSpace.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeLinSpace.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeLinSpace.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeLinSpace.cpp.o: ../source/shape/ShapeLinSpace.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeMatMul.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeMatMul.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeMatMul.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeMatMul.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeMatMul.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeMatMul.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeMatMul.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeMatMul.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeMatMul.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeMatMul.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeMatMul.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeMatMul.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeMatMul.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeMatMul.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeMatMul.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeMatMul.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeMatMul.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeMatMul.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeMatMul.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeMatMul.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeMatMul.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeMatMul.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeMatMul.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeMatMul.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeMatMul.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeMatMul.cpp.o: ../source/core/OpCommonUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeMatMul.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeMatMul.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeMatMul.cpp.o: ../source/shape/ShapeMatMul.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeMoments.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeMoments.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeMoments.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeMoments.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeMoments.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeMoments.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeMoments.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeMoments.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeMoments.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeMoments.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeMoments.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeMoments.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeMoments.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeMoments.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeMoments.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeMoments.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeMoments.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeMoments.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeMoments.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeMoments.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeMoments.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeMoments.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeMoments.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeMoments.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeMoments.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeMoments.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeMoments.cpp.o: ../source/shape/ShapeMoments.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeNonMaxSuppressionV2.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeNonMaxSuppressionV2.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeNonMaxSuppressionV2.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeNonMaxSuppressionV2.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeNonMaxSuppressionV2.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeNonMaxSuppressionV2.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeNonMaxSuppressionV2.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeNonMaxSuppressionV2.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeNonMaxSuppressionV2.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeNonMaxSuppressionV2.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeNonMaxSuppressionV2.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeNonMaxSuppressionV2.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeNonMaxSuppressionV2.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeNonMaxSuppressionV2.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeNonMaxSuppressionV2.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeNonMaxSuppressionV2.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeNonMaxSuppressionV2.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeNonMaxSuppressionV2.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeNonMaxSuppressionV2.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeNonMaxSuppressionV2.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeNonMaxSuppressionV2.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeNonMaxSuppressionV2.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeNonMaxSuppressionV2.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeNonMaxSuppressionV2.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeNonMaxSuppressionV2.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeNonMaxSuppressionV2.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeNonMaxSuppressionV2.cpp.o: ../source/shape/ShapeNonMaxSuppressionV2.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeOneHot.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeOneHot.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeOneHot.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeOneHot.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeOneHot.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeOneHot.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeOneHot.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeOneHot.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeOneHot.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeOneHot.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeOneHot.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeOneHot.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeOneHot.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeOneHot.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeOneHot.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeOneHot.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeOneHot.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeOneHot.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeOneHot.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeOneHot.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeOneHot.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeOneHot.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeOneHot.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeOneHot.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeOneHot.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeOneHot.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeOneHot.cpp.o: ../source/shape/ShapeOneHot.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapePack.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePack.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePack.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePack.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePack.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePack.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePack.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePack.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePack.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePack.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePack.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePack.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePack.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePack.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePack.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePack.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePack.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePack.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePack.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePack.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePack.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePack.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePack.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePack.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePack.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePack.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePack.cpp.o: ../source/shape/ShapePack.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapePadding.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePadding.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePadding.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePadding.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePadding.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePadding.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePadding.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePadding.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePadding.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePadding.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePadding.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePadding.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePadding.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePadding.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePadding.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePadding.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePadding.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePadding.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePadding.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePadding.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePadding.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePadding.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePadding.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePadding.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePadding.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePadding.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePadding.cpp.o: ../source/shape/ShapePadding.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapePermute.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePermute.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePermute.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePermute.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePermute.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePermute.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePermute.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePermute.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePermute.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePermute.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePermute.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePermute.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePermute.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePermute.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePermute.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePermute.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePermute.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePermute.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePermute.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePermute.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePermute.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePermute.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePermute.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePermute.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePermute.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePermute.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePermute.cpp.o: ../source/shape/ShapePermute.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapePlugin.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePlugin.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePlugin.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePlugin.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePlugin.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePlugin.cpp.o: ../include/MNN/Interpreter.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePlugin.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePlugin.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePlugin.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePlugin.cpp.o: ../include/MNN/plugin/PluginContext.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePlugin.cpp.o: ../include/MNN/plugin/PluginShapeInference.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePlugin.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePlugin.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePlugin.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePlugin.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePlugin.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePlugin.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePlugin.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePlugin.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePlugin.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePlugin.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePlugin.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePlugin.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePlugin.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePlugin.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePlugin.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePlugin.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePlugin.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePlugin.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePlugin.cpp.o: ../source/shape/ShapePlugin.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapePool.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePool.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePool.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePool.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePool.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePool.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePool.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePool.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePool.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePool.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePool.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePool.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePool.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePool.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePool.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePool.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePool.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePool.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePool.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePool.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePool.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePool.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePool.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePool.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePool.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePool.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePool.cpp.o: ../source/shape/ShapePool.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapePool3D.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePool3D.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePool3D.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePool3D.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePool3D.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePool3D.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePool3D.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePool3D.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePool3D.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePool3D.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePool3D.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePool3D.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePool3D.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePool3D.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePool3D.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePool3D.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePool3D.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePool3D.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePool3D.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePool3D.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePool3D.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePool3D.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePool3D.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePool3D.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePool3D.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePool3D.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePool3D.cpp.o: ../source/shape/ShapePool3D.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapePriorbox.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePriorbox.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePriorbox.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePriorbox.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePriorbox.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePriorbox.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePriorbox.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePriorbox.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePriorbox.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePriorbox.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePriorbox.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePriorbox.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePriorbox.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePriorbox.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePriorbox.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePriorbox.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePriorbox.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePriorbox.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePriorbox.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePriorbox.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePriorbox.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePriorbox.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePriorbox.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapePriorbox.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePriorbox.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePriorbox.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapePriorbox.cpp.o: ../source/shape/ShapePriorbox.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeProposal.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeProposal.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeProposal.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeProposal.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeProposal.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeProposal.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeProposal.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeProposal.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeProposal.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeProposal.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeProposal.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeProposal.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeProposal.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeProposal.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeProposal.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeProposal.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeProposal.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeProposal.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeProposal.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeProposal.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeProposal.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeProposal.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeProposal.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeProposal.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeProposal.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeProposal.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeProposal.cpp.o: ../source/shape/ShapeProposal.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedAvgPool.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedAvgPool.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedAvgPool.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedAvgPool.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedAvgPool.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedAvgPool.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedAvgPool.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedAvgPool.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedAvgPool.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedAvgPool.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedAvgPool.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedAvgPool.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedAvgPool.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedAvgPool.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedAvgPool.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedAvgPool.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedAvgPool.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedAvgPool.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedAvgPool.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedAvgPool.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedAvgPool.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedAvgPool.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedAvgPool.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedAvgPool.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedAvgPool.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedAvgPool.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedAvgPool.cpp.o: ../source/shape/ShapeQuantizedAvgPool.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedMaxPool.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedMaxPool.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedMaxPool.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedMaxPool.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedMaxPool.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedMaxPool.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedMaxPool.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedMaxPool.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedMaxPool.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedMaxPool.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedMaxPool.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedMaxPool.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedMaxPool.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedMaxPool.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedMaxPool.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedMaxPool.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedMaxPool.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedMaxPool.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedMaxPool.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedMaxPool.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedMaxPool.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedMaxPool.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedMaxPool.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedMaxPool.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedMaxPool.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedMaxPool.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeQuantizedMaxPool.cpp.o: ../source/shape/ShapeQuantizedMaxPool.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeRNNSequenceGRU.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRNNSequenceGRU.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRNNSequenceGRU.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRNNSequenceGRU.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeRNNSequenceGRU.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRNNSequenceGRU.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRNNSequenceGRU.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRNNSequenceGRU.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeRNNSequenceGRU.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRNNSequenceGRU.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRNNSequenceGRU.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRNNSequenceGRU.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRNNSequenceGRU.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRNNSequenceGRU.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRNNSequenceGRU.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRNNSequenceGRU.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRNNSequenceGRU.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRNNSequenceGRU.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeRNNSequenceGRU.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeRNNSequenceGRU.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeRNNSequenceGRU.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeRNNSequenceGRU.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRNNSequenceGRU.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRNNSequenceGRU.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeRNNSequenceGRU.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeRNNSequenceGRU.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeRNNSequenceGRU.cpp.o: ../source/shape/ShapeRNNSequenceGRU.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeROIAlign.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIAlign.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIAlign.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIAlign.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIAlign.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIAlign.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIAlign.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIAlign.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIAlign.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIAlign.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIAlign.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIAlign.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIAlign.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIAlign.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIAlign.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIAlign.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIAlign.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIAlign.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIAlign.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIAlign.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIAlign.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIAlign.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIAlign.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIAlign.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIAlign.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIAlign.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIAlign.cpp.o: ../source/shape/ShapeROIAlign.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeROIPooling.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIPooling.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIPooling.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIPooling.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIPooling.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIPooling.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIPooling.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIPooling.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIPooling.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIPooling.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIPooling.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIPooling.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIPooling.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIPooling.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIPooling.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIPooling.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIPooling.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIPooling.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIPooling.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIPooling.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIPooling.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIPooling.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIPooling.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIPooling.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIPooling.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIPooling.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeROIPooling.cpp.o: ../source/shape/ShapeROIPooling.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeRandomUniform.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRandomUniform.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRandomUniform.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRandomUniform.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeRandomUniform.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRandomUniform.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRandomUniform.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRandomUniform.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeRandomUniform.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRandomUniform.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRandomUniform.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRandomUniform.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRandomUniform.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRandomUniform.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRandomUniform.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRandomUniform.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRandomUniform.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRandomUniform.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeRandomUniform.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeRandomUniform.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeRandomUniform.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeRandomUniform.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRandomUniform.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRandomUniform.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeRandomUniform.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeRandomUniform.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeRandomUniform.cpp.o: ../source/shape/ShapeRandomUniform.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeRange.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRange.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRange.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRange.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeRange.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRange.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRange.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRange.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeRange.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRange.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRange.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRange.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRange.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRange.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRange.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRange.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRange.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRange.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeRange.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeRange.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeRange.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeRange.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRange.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeRange.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeRange.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeRange.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeRange.cpp.o: ../source/shape/ShapeRange.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeReduction.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeReduction.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeReduction.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeReduction.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeReduction.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeReduction.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeReduction.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeReduction.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeReduction.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeReduction.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeReduction.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeReduction.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeReduction.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeReduction.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeReduction.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeReduction.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeReduction.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeReduction.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeReduction.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeReduction.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeReduction.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeReduction.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeReduction.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeReduction.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeReduction.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeReduction.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeReduction.cpp.o: ../source/shape/ShapeReduction.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeRegister.cpp.o: ../source/shape/ShapeRegister.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeReshape.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeReshape.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeReshape.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeReshape.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeReshape.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeReshape.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeReshape.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeReshape.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeReshape.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeReshape.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeReshape.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeReshape.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeReshape.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeReshape.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeReshape.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeReshape.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeReshape.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeReshape.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeReshape.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeReshape.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeReshape.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeReshape.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeReshape.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeReshape.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeReshape.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeReshape.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeReshape.cpp.o: ../source/shape/ShapeReshape.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeResize.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeResize.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeResize.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeResize.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeResize.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeResize.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeResize.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeResize.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeResize.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeResize.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeResize.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeResize.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeResize.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeResize.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeResize.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeResize.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeResize.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeResize.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeResize.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeResize.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeResize.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeResize.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeResize.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeResize.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeResize.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeResize.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeResize.cpp.o: ../source/shape/ShapeResize.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeScatterNd.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeScatterNd.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeScatterNd.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeScatterNd.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeScatterNd.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeScatterNd.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeScatterNd.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeScatterNd.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeScatterNd.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeScatterNd.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeScatterNd.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeScatterNd.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeScatterNd.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeScatterNd.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeScatterNd.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeScatterNd.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeScatterNd.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeScatterNd.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeScatterNd.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeScatterNd.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeScatterNd.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeScatterNd.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeScatterNd.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeScatterNd.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeScatterNd.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeScatterNd.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeScatterNd.cpp.o: ../source/shape/ShapeScatterNd.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeSegmentMean.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSegmentMean.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSegmentMean.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSegmentMean.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSegmentMean.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSegmentMean.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSegmentMean.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSegmentMean.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSegmentMean.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSegmentMean.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSegmentMean.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSegmentMean.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSegmentMean.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSegmentMean.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSegmentMean.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSegmentMean.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSegmentMean.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSegmentMean.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSegmentMean.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSegmentMean.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSegmentMean.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSegmentMean.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSegmentMean.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSegmentMean.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSegmentMean.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSegmentMean.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSegmentMean.cpp.o: ../source/shape/ShapeSegmentMean.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeSelect.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSelect.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSelect.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSelect.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSelect.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSelect.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSelect.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSelect.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSelect.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSelect.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSelect.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSelect.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSelect.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSelect.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSelect.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSelect.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSelect.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSelect.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSelect.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSelect.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSelect.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSelect.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSelect.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSelect.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSelect.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSelect.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSelect.cpp.o: ../source/shape/ShapeSelect.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeSetDiff1D.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSetDiff1D.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSetDiff1D.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSetDiff1D.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSetDiff1D.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSetDiff1D.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSetDiff1D.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSetDiff1D.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSetDiff1D.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSetDiff1D.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSetDiff1D.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSetDiff1D.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSetDiff1D.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSetDiff1D.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSetDiff1D.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSetDiff1D.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSetDiff1D.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSetDiff1D.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSetDiff1D.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSetDiff1D.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSetDiff1D.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSetDiff1D.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSetDiff1D.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSetDiff1D.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSetDiff1D.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSetDiff1D.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSetDiff1D.cpp.o: ../source/shape/ShapeSetDiff1D.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeShape.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeShape.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeShape.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeShape.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeShape.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeShape.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeShape.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeShape.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeShape.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeShape.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeShape.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeShape.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeShape.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeShape.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeShape.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeShape.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeShape.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeShape.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeShape.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeShape.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeShape.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeShape.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeShape.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeShape.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeShape.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeShape.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeShape.cpp.o: ../source/shape/ShapeShape.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeSize.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSize.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSize.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSize.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSize.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSize.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSize.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSize.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSize.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSize.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSize.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSize.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSize.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSize.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSize.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSize.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSize.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSize.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSize.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSize.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSize.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSize.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSize.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSize.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSize.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSize.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSize.cpp.o: ../source/shape/ShapeSize.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeSlice.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSlice.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSlice.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSlice.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSlice.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSlice.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSlice.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSlice.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSlice.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSlice.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSlice.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSlice.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSlice.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSlice.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSlice.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSlice.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSlice.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSlice.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSlice.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSlice.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSlice.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSlice.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSlice.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSlice.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSlice.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSlice.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSlice.cpp.o: ../source/shape/ShapeSlice.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeSliceTf.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSliceTf.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSliceTf.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSliceTf.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSliceTf.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSliceTf.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSliceTf.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSliceTf.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSliceTf.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSliceTf.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSliceTf.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSliceTf.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSliceTf.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSliceTf.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSliceTf.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSliceTf.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSliceTf.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSliceTf.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSliceTf.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSliceTf.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSliceTf.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSliceTf.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSliceTf.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSliceTf.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSliceTf.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSliceTf.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSliceTf.cpp.o: ../source/shape/ShapeSliceTf.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToBatchND.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToBatchND.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToBatchND.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToBatchND.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToBatchND.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToBatchND.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToBatchND.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToBatchND.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToBatchND.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToBatchND.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToBatchND.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToBatchND.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToBatchND.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToBatchND.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToBatchND.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToBatchND.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToBatchND.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToBatchND.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToBatchND.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToBatchND.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToBatchND.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToBatchND.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToBatchND.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToBatchND.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToBatchND.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToBatchND.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToBatchND.cpp.o: ../source/shape/ShapeSpaceToBatchND.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToDepth.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToDepth.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToDepth.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToDepth.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToDepth.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToDepth.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToDepth.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToDepth.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToDepth.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToDepth.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToDepth.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToDepth.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToDepth.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToDepth.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToDepth.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToDepth.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToDepth.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToDepth.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToDepth.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToDepth.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToDepth.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToDepth.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToDepth.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToDepth.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToDepth.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToDepth.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSpaceToDepth.cpp.o: ../source/shape/ShapeSpaceToDepth.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeSplitGelu.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSplitGelu.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSplitGelu.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSplitGelu.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSplitGelu.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSplitGelu.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSplitGelu.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSplitGelu.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSplitGelu.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSplitGelu.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSplitGelu.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSplitGelu.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSplitGelu.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSplitGelu.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSplitGelu.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSplitGelu.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSplitGelu.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSplitGelu.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSplitGelu.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSplitGelu.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSplitGelu.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSplitGelu.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSplitGelu.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSplitGelu.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSplitGelu.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSplitGelu.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSplitGelu.cpp.o: ../source/shape/ShapeSplitGelu.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeSqueeze.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSqueeze.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSqueeze.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSqueeze.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSqueeze.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSqueeze.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSqueeze.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSqueeze.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSqueeze.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSqueeze.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSqueeze.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSqueeze.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSqueeze.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSqueeze.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSqueeze.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSqueeze.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSqueeze.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSqueeze.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSqueeze.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSqueeze.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSqueeze.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSqueeze.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSqueeze.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSqueeze.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSqueeze.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSqueeze.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSqueeze.cpp.o: ../source/shape/ShapeSqueeze.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeStft.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeStft.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeStft.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeStft.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeStft.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeStft.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeStft.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeStft.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeStft.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeStft.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeStft.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeStft.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeStft.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeStft.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeStft.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeStft.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeStft.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeStft.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeStft.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeStft.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeStft.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeStft.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeStft.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeStft.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeStft.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeStft.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeStft.cpp.o: ../source/shape/ShapeStft.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeStridedSlice.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeStridedSlice.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeStridedSlice.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeStridedSlice.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeStridedSlice.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeStridedSlice.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeStridedSlice.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeStridedSlice.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeStridedSlice.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeStridedSlice.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeStridedSlice.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeStridedSlice.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeStridedSlice.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeStridedSlice.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeStridedSlice.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeStridedSlice.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeStridedSlice.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeStridedSlice.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeStridedSlice.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeStridedSlice.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeStridedSlice.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeStridedSlice.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeStridedSlice.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeStridedSlice.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeStridedSlice.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeStridedSlice.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeStridedSlice.cpp.o: ../source/shape/ShapeStridedSlice.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeSvd.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSvd.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSvd.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSvd.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSvd.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSvd.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSvd.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSvd.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSvd.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSvd.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSvd.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSvd.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSvd.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSvd.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSvd.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSvd.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSvd.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSvd.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSvd.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSvd.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSvd.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSvd.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSvd.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeSvd.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSvd.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSvd.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeSvd.cpp.o: ../source/shape/ShapeSvd.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorArray.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorArray.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorArray.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorArray.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorArray.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorArray.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorArray.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorArray.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorArray.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorArray.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorArray.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorArray.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorArray.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorArray.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorArray.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorArray.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorArray.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorArray.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorArray.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorArray.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorArray.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorArray.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorArray.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorArray.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorArray.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorArray.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorArray.cpp.o: ../source/shape/ShapeTensorArray.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorConvert.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorConvert.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorConvert.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorConvert.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorConvert.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorConvert.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorConvert.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorConvert.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorConvert.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorConvert.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorConvert.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorConvert.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorConvert.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorConvert.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorConvert.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorConvert.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorConvert.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorConvert.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorConvert.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorConvert.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorConvert.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorConvert.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorConvert.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorConvert.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorConvert.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorConvert.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTensorConvert.cpp.o: ../source/shape/ShapeTensorConvert.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeTile.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTile.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTile.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTile.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTile.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTile.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTile.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTile.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTile.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTile.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTile.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTile.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTile.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTile.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTile.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTile.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTile.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTile.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTile.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTile.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTile.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTile.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTile.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTile.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTile.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTile.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTile.cpp.o: ../source/shape/ShapeTile.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeTopKV2.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTopKV2.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTopKV2.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTopKV2.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTopKV2.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTopKV2.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTopKV2.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTopKV2.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTopKV2.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTopKV2.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTopKV2.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTopKV2.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTopKV2.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTopKV2.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTopKV2.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTopKV2.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTopKV2.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTopKV2.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTopKV2.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTopKV2.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTopKV2.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTopKV2.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTopKV2.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTopKV2.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTopKV2.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTopKV2.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTopKV2.cpp.o: ../source/shape/ShapeTopKV2.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeTranspose.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTranspose.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTranspose.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTranspose.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTranspose.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTranspose.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTranspose.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTranspose.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTranspose.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTranspose.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTranspose.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTranspose.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTranspose.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTranspose.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTranspose.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTranspose.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTranspose.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTranspose.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTranspose.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTranspose.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTranspose.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTranspose.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTranspose.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeTranspose.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTranspose.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTranspose.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeTranspose.cpp.o: ../source/shape/ShapeTranspose.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeUnique.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnique.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnique.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnique.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnique.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnique.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnique.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnique.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnique.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnique.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnique.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnique.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnique.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnique.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnique.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnique.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnique.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnique.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnique.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnique.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnique.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnique.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnique.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnique.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnique.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnique.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnique.cpp.o: ../source/shape/ShapeUnique.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeUnpack.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnpack.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnpack.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnpack.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnpack.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnpack.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnpack.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnpack.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnpack.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnpack.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnpack.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnpack.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnpack.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnpack.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnpack.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnpack.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnpack.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnpack.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnpack.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnpack.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnpack.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnpack.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnpack.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnpack.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnpack.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnpack.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnpack.cpp.o: ../source/shape/ShapeUnpack.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeUnravelIndex.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnravelIndex.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnravelIndex.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnravelIndex.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnravelIndex.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnravelIndex.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnravelIndex.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnravelIndex.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnravelIndex.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnravelIndex.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnravelIndex.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnravelIndex.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnravelIndex.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnravelIndex.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnravelIndex.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnravelIndex.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnravelIndex.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnravelIndex.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnravelIndex.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnravelIndex.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnravelIndex.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnravelIndex.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnravelIndex.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnravelIndex.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnravelIndex.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnravelIndex.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeUnravelIndex.cpp.o: ../source/shape/ShapeUnravelIndex.cpp

CMakeFiles/MNNTransform.dir/source/shape/ShapeWhere.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeWhere.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeWhere.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeWhere.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeWhere.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeWhere.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeWhere.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeWhere.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeWhere.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeWhere.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeWhere.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeWhere.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeWhere.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeWhere.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeWhere.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeWhere.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeWhere.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeWhere.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeWhere.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeWhere.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeWhere.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeWhere.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeWhere.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/ShapeWhere.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeWhere.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeWhere.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/ShapeWhere.cpp.o: ../source/shape/ShapeWhere.cpp

CMakeFiles/MNNTransform.dir/source/shape/SizeComputer.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/SizeComputer.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/SizeComputer.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/SizeComputer.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/SizeComputer.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/SizeComputer.cpp.o: ../include/MNN/Interpreter.hpp
CMakeFiles/MNNTransform.dir/source/shape/SizeComputer.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/SizeComputer.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/SizeComputer.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/SizeComputer.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/SizeComputer.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/SizeComputer.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/SizeComputer.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/SizeComputer.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/SizeComputer.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/SizeComputer.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/SizeComputer.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/SizeComputer.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/SizeComputer.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/SizeComputer.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/SizeComputer.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/SizeComputer.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/SizeComputer.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/SizeComputer.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/SizeComputer.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/SizeComputer.cpp.o: ../source/core/Schedule.hpp
CMakeFiles/MNNTransform.dir/source/shape/SizeComputer.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/SizeComputer.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/SizeComputer.cpp.o: ../source/utils/InitNet.hpp
CMakeFiles/MNNTransform.dir/source/shape/SizeComputer.cpp.o: ../source/shape/SizeComputer.cpp

CMakeFiles/MNNTransform.dir/source/shape/render/ShapeRasterAndInterpolate.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNTransform.dir/source/shape/render/ShapeRasterAndInterpolate.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNTransform.dir/source/shape/render/ShapeRasterAndInterpolate.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNTransform.dir/source/shape/render/ShapeRasterAndInterpolate.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNTransform.dir/source/shape/render/ShapeRasterAndInterpolate.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNTransform.dir/source/shape/render/ShapeRasterAndInterpolate.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNTransform.dir/source/shape/render/ShapeRasterAndInterpolate.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNTransform.dir/source/shape/render/ShapeRasterAndInterpolate.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNTransform.dir/source/shape/render/ShapeRasterAndInterpolate.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/render/ShapeRasterAndInterpolate.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNTransform.dir/source/shape/render/ShapeRasterAndInterpolate.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNTransform.dir/source/shape/render/ShapeRasterAndInterpolate.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/render/ShapeRasterAndInterpolate.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNTransform.dir/source/shape/render/ShapeRasterAndInterpolate.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNTransform.dir/source/shape/render/ShapeRasterAndInterpolate.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNTransform.dir/source/shape/render/ShapeRasterAndInterpolate.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNTransform.dir/source/shape/render/ShapeRasterAndInterpolate.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNTransform.dir/source/shape/render/ShapeRasterAndInterpolate.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNTransform.dir/source/shape/render/ShapeRasterAndInterpolate.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNTransform.dir/source/shape/render/ShapeRasterAndInterpolate.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNTransform.dir/source/shape/render/ShapeRasterAndInterpolate.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNNTransform.dir/source/shape/render/ShapeRasterAndInterpolate.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNTransform.dir/source/shape/render/ShapeRasterAndInterpolate.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNTransform.dir/source/shape/render/ShapeRasterAndInterpolate.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNTransform.dir/source/shape/render/ShapeRasterAndInterpolate.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNTransform.dir/source/shape/render/ShapeRasterAndInterpolate.cpp.o: ../source/shape/SizeComputer.hpp
CMakeFiles/MNNTransform.dir/source/shape/render/ShapeRasterAndInterpolate.cpp.o: ../source/shape/render/ShapeRasterAndInterpolate.cpp

