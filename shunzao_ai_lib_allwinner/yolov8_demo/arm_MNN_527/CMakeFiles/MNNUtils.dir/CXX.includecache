#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

../3rd_party/flatbuffers/include/flatbuffers/base.h
assert.h
-
cstdint
-
cstddef
-
cstdlib
-
cstring
-
crtdbg.h
-
utility.h
-
utility
-
string
-
type_traits
-
vector
-
set
-
algorithm
-
iterator
-
memory
-
functional
-
stl_emulation.h
../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
string_view
-
experimental/string_view
-

../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
base.h
../3rd_party/flatbuffers/include/flatbuffers/base.h
cmath
-

../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
string
-
type_traits
-
vector
-
memory
-
limits
-
cctype
-

../3rd_party/half/half.hpp
utility
-
algorithm
-
iostream
-
limits
-
climits
-
cmath
-
cstring
-
cstdlib
-
type_traits
-
cstdint
-
functional
-

../include/MNN/ErrorCode.hpp

../include/MNN/HalideRuntime.h
stddef.h
-
stdint.h
-
stdbool.h
-

../include/MNN/Interpreter.hpp
functional
-
map
-
memory
-
string
-
MNN/ErrorCode.hpp
-
MNN/MNNForwardType.h
-
MNN/Tensor.hpp
-

../include/MNN/MNNDefine.h
assert.h
-
stdio.h
-
TargetConditionals.h
-
hilog/log.h
-
android/log.h
-
syslog.h
-

../include/MNN/MNNForwardType.h
stdint.h
-
stddef.h
-

../include/MNN/Tensor.hpp
vector
-
MNN/HalideRuntime.h
-
MNN/MNNDefine.h
-

../schema/current/CaffeOp_generated.h
Tensor_generated.h
../schema/current/Tensor_generated.h
Type_generated.h
../schema/current/Type_generated.h

../schema/current/ExtraInfo_generated.h
Tensor_generated.h
../schema/current/Tensor_generated.h
Type_generated.h
../schema/current/Type_generated.h

../schema/current/MNN_generated.h
CaffeOp_generated.h
../schema/current/CaffeOp_generated.h
ExtraInfo_generated.h
../schema/current/ExtraInfo_generated.h
TFQuantizeOp_generated.h
../schema/current/TFQuantizeOp_generated.h
Tensor_generated.h
../schema/current/Tensor_generated.h
TensorflowOp_generated.h
../schema/current/TensorflowOp_generated.h
Type_generated.h
../schema/current/Type_generated.h
UserDefine_generated.h
../schema/current/UserDefine_generated.h

../schema/current/TFQuantizeOp_generated.h
CaffeOp_generated.h
../schema/current/CaffeOp_generated.h
Tensor_generated.h
../schema/current/Tensor_generated.h
Type_generated.h
../schema/current/Type_generated.h

../schema/current/Tensor_generated.h
Type_generated.h
../schema/current/Type_generated.h

../schema/current/TensorflowOp_generated.h
Tensor_generated.h
../schema/current/Tensor_generated.h
Type_generated.h
../schema/current/Type_generated.h

../schema/current/Type_generated.h
flatbuffers/flatbuffers.h
../schema/current/flatbuffers/flatbuffers.h

../schema/current/UserDefine_generated.h
Tensor_generated.h
../schema/current/Tensor_generated.h
Type_generated.h
../schema/current/Type_generated.h

../source/core/AutoStorage.h
stdint.h
-
string.h
-
MNNMemoryUtils.h
../source/core/MNNMemoryUtils.h

../source/core/Backend.hpp
MNN/MNNForwardType.h
-
MNN/ErrorCode.hpp
-
map
-
Command.hpp
../source/core/Command.hpp
NonCopyable.hpp
../source/core/NonCopyable.hpp
BufferAllocator.hpp
../source/core/BufferAllocator.hpp
future
-
atomic
-

../source/core/BufferAllocator.hpp
map
-
set
-
memory
-
vector
-
MNNMemoryUtils.h
../source/core/MNNMemoryUtils.h
NonCopyable.hpp
../source/core/NonCopyable.hpp
AutoStorage.h
../source/core/AutoStorage.h
MNN/Tensor.hpp
-
MNN/ErrorCode.hpp
-

../source/core/Command.hpp
MNN/Tensor.hpp
-
AutoStorage.h
../source/core/AutoStorage.h
string
-
memory
-

../source/core/FileLoader.hpp
vector
-
mutex
-
string
-
core/AutoStorage.h
../source/core/core/AutoStorage.h

../source/core/MNNMemoryUtils.h
stdio.h
-
core/Macro.h
../source/core/core/Macro.h

../source/core/Macro.h
MNN/MNNDefine.h
-
BaseTsd.h
-

../source/core/NonCopyable.hpp

../source/core/OpCommonUtils.hpp
MNN/Tensor.hpp
-
TensorUtils.hpp
../source/core/TensorUtils.hpp
FileLoader.hpp
../source/core/FileLoader.hpp

../source/core/Schedule.hpp
stdio.h
-
MNN/Interpreter.hpp
-
map
-
string
-
vector
-
array
-
core/Backend.hpp
../source/core/core/Backend.hpp
core/Command.hpp
../source/core/core/Command.hpp

../source/core/TensorUtils.hpp
MNN/Tensor.hpp
-
Backend.hpp
../source/core/Backend.hpp
AutoStorage.h
../source/core/AutoStorage.h
Tensor_generated.h
../source/core/Tensor_generated.h

../source/utils/JNIHelper.hpp
jni.h
-
android/log.h
-
string
-
unistd.h
-
MNN/MNNDefine.h
-

/root/pan/shunzao_ai_lib-develop/MNN-master/source/utils/InitNet.cpp
InitNet.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/utils/InitNet.hpp
core/TensorUtils.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/utils/core/TensorUtils.hpp
unordered_map
-
core/OpCommonUtils.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/utils/core/OpCommonUtils.hpp
half.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/utils/half.hpp

/root/pan/shunzao_ai_lib-develop/MNN-master/source/utils/InitNet.hpp
MNN_generated.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/utils/MNN_generated.h
core/TensorUtils.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/utils/core/TensorUtils.hpp
core/Schedule.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/utils/core/Schedule.hpp

/root/pan/shunzao_ai_lib-develop/MNN-master/source/utils/JNIHelper.cpp
utils/JNIHelper.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/utils/utils/JNIHelper.hpp

