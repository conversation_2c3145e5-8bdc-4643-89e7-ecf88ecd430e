# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "CXX"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_CXX
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/utils/InitNet.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/utils/JNIHelper.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNUtils.dir/source/utils/JNIHelper.cpp.o"
  )
set(CMAKE_CXX_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_CXX
  "MNN_KLEIDIAI_ENABLED=1"
  "MNN_SME2"
  "MNN_SUPPORT_DEPRECATED_OPV2"
  "MNN_SUPPORT_QUANT_EXTEND"
  "MNN_USE_NEON"
  "MNN_USE_THREAD_POOL"
  )

# The include file search paths:
set(CMAKE_CXX_TARGET_INCLUDE_PATH
  "../include"
  "../source"
  "../express"
  "../tools"
  "../codegen"
  "../schema/current"
  "../3rd_party"
  "../3rd_party/flatbuffers/include"
  "../3rd_party/half"
  "../3rd_party/imageHelper"
  "../3rd_party/OpenCLHeaders"
  "_deps/kleidiai-v1.9.0"
  "_deps/kleidiai-v1.9.0/kai"
  "_deps/kleidiai-v1.9.0/kai/ukernels"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/matmul_clamp_f32_qai8dxp_qsi4cxp"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/matmul_clamp_f16_qsi8d32p_qai4c32p"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/matmul_clamp_f32_qsi8d32p_qai4c32p"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/pack"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/matmul_clamp_f32_f32p_f32p"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/matmul_clamp_f32_f32_f32p"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/matmul_clamp_f16_f16p_f16p"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/matmul_clamp_f16_f16_f16p"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/imatmul_clamp_f32_f32p_f32p"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/imatmul_clamp_f16_f16p_f16p"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
