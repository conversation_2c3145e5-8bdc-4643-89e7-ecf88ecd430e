# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /root/pan/shunzao_ai_lib-develop/MNN-master

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm

# Include any dependencies generated for this target.
include CMakeFiles/MNNUtils.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/MNNUtils.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/MNNUtils.dir/flags.make

CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.o: CMakeFiles/MNNUtils.dir/flags.make
CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.o: ../source/utils/InitNet.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/utils/InitNet.cpp

CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/utils/InitNet.cpp > CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.i

CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/utils/InitNet.cpp -o CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.s

CMakeFiles/MNNUtils.dir/source/utils/JNIHelper.cpp.o: CMakeFiles/MNNUtils.dir/flags.make
CMakeFiles/MNNUtils.dir/source/utils/JNIHelper.cpp.o: ../source/utils/JNIHelper.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/MNNUtils.dir/source/utils/JNIHelper.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/MNNUtils.dir/source/utils/JNIHelper.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/utils/JNIHelper.cpp

CMakeFiles/MNNUtils.dir/source/utils/JNIHelper.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNNUtils.dir/source/utils/JNIHelper.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/utils/JNIHelper.cpp > CMakeFiles/MNNUtils.dir/source/utils/JNIHelper.cpp.i

CMakeFiles/MNNUtils.dir/source/utils/JNIHelper.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNNUtils.dir/source/utils/JNIHelper.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/utils/JNIHelper.cpp -o CMakeFiles/MNNUtils.dir/source/utils/JNIHelper.cpp.s

MNNUtils: CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.o
MNNUtils: CMakeFiles/MNNUtils.dir/source/utils/JNIHelper.cpp.o
MNNUtils: CMakeFiles/MNNUtils.dir/build.make

.PHONY : MNNUtils

# Rule to build all files generated by this target.
CMakeFiles/MNNUtils.dir/build: MNNUtils

.PHONY : CMakeFiles/MNNUtils.dir/build

CMakeFiles/MNNUtils.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/MNNUtils.dir/cmake_clean.cmake
.PHONY : CMakeFiles/MNNUtils.dir/clean

CMakeFiles/MNNUtils.dir/depend:
	cd /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /root/pan/shunzao_ai_lib-develop/MNN-master /root/pan/shunzao_ai_lib-develop/MNN-master /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNNUtils.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/MNNUtils.dir/depend

