# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.o
 ../3rd_party/flatbuffers/include/flatbuffers/base.h
 ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
 ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
 ../3rd_party/half/half.hpp
 ../include/MNN/ErrorCode.hpp
 ../include/MNN/HalideRuntime.h
 ../include/MNN/Interpreter.hpp
 ../include/MNN/MNNDefine.h
 ../include/MNN/MNNForwardType.h
 ../include/MNN/Tensor.hpp
 ../schema/current/CaffeOp_generated.h
 ../schema/current/ExtraInfo_generated.h
 ../schema/current/MNN_generated.h
 ../schema/current/TFQuantizeOp_generated.h
 ../schema/current/Tensor_generated.h
 ../schema/current/TensorflowOp_generated.h
 ../schema/current/Type_generated.h
 ../schema/current/UserDefine_generated.h
 ../source/core/AutoStorage.h
 ../source/core/Backend.hpp
 ../source/core/BufferAllocator.hpp
 ../source/core/Command.hpp
 ../source/core/FileLoader.hpp
 ../source/core/MNNMemoryUtils.h
 ../source/core/Macro.h
 ../source/core/NonCopyable.hpp
 ../source/core/OpCommonUtils.hpp
 ../source/core/Schedule.hpp
 ../source/core/TensorUtils.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/utils/InitNet.cpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/utils/InitNet.hpp
CMakeFiles/MNNUtils.dir/source/utils/JNIHelper.cpp.o
 ../include/MNN/MNNDefine.h
 ../source/utils/JNIHelper.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/utils/JNIHelper.cpp
