# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.o: ../3rd_party/half/half.hpp
CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.o: ../include/MNN/Interpreter.hpp
CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.o: ../source/core/OpCommonUtils.hpp
CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.o: ../source/core/Schedule.hpp
CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.o: ../source/utils/InitNet.cpp
CMakeFiles/MNNUtils.dir/source/utils/InitNet.cpp.o: ../source/utils/InitNet.hpp

CMakeFiles/MNNUtils.dir/source/utils/JNIHelper.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNUtils.dir/source/utils/JNIHelper.cpp.o: ../source/utils/JNIHelper.hpp
CMakeFiles/MNNUtils.dir/source/utils/JNIHelper.cpp.o: ../source/utils/JNIHelper.cpp

