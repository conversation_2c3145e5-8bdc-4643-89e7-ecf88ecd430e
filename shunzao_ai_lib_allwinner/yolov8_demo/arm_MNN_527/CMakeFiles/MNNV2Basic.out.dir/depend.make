# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

CMakeFiles/MNNV2Basic.out.dir/tools/cpp/MNNV2Basic.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNNV2Basic.out.dir/tools/cpp/MNNV2Basic.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNNV2Basic.out.dir/tools/cpp/MNNV2Basic.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNNV2Basic.out.dir/tools/cpp/MNNV2Basic.cpp.o: ../include/MNN/AutoTime.hpp
CMakeFiles/MNNV2Basic.out.dir/tools/cpp/MNNV2Basic.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNNV2Basic.out.dir/tools/cpp/MNNV2Basic.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNNV2Basic.out.dir/tools/cpp/MNNV2Basic.cpp.o: ../include/MNN/Interpreter.hpp
CMakeFiles/MNNV2Basic.out.dir/tools/cpp/MNNV2Basic.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNNV2Basic.out.dir/tools/cpp/MNNV2Basic.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNNV2Basic.out.dir/tools/cpp/MNNV2Basic.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNNV2Basic.out.dir/tools/cpp/MNNV2Basic.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNNV2Basic.out.dir/tools/cpp/MNNV2Basic.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNNV2Basic.out.dir/tools/cpp/MNNV2Basic.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNNV2Basic.out.dir/tools/cpp/MNNV2Basic.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNNV2Basic.out.dir/tools/cpp/MNNV2Basic.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNNV2Basic.out.dir/tools/cpp/MNNV2Basic.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNNV2Basic.out.dir/tools/cpp/MNNV2Basic.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNNV2Basic.out.dir/tools/cpp/MNNV2Basic.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNNV2Basic.out.dir/tools/cpp/MNNV2Basic.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNNV2Basic.out.dir/tools/cpp/MNNV2Basic.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNNV2Basic.out.dir/tools/cpp/MNNV2Basic.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNNV2Basic.out.dir/tools/cpp/MNNV2Basic.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNNV2Basic.out.dir/tools/cpp/MNNV2Basic.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNNV2Basic.out.dir/tools/cpp/MNNV2Basic.cpp.o: ../source/core/Macro.h
CMakeFiles/MNNV2Basic.out.dir/tools/cpp/MNNV2Basic.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNNV2Basic.out.dir/tools/cpp/MNNV2Basic.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNNV2Basic.out.dir/tools/cpp/MNNV2Basic.cpp.o: ../tools/cpp/MNNV2Basic.cpp

