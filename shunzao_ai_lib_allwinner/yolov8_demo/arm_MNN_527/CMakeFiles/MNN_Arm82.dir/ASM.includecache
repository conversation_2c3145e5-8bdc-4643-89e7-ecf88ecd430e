#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

../source/backend/arm82/asm/MNNAsmGlobal.h

/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/Arm82MNNPackForMatMul_A.S
MNNAsmGlobal.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNAsmGlobal.h

/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/CountMinMaxValue_FP16.S
MNNAsmGlobal.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNAsmGlobal.h

/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNConvRunForLineDepthwiseFP16.S
MNNAsmGlobal.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNAsmGlobal.h

/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNConvWinoSourceTransformUnit6x6FP16.S
MNNAsmGlobal.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNAsmGlobal.h

/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNDepthwiseConvFastKernelFP16.S
MNNAsmGlobal.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNAsmGlobal.h

/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNExpFP16.S
MNNAsmGlobal.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNAsmGlobal.h

/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNGeluFP16.S
MNNAsmGlobal.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNAsmGlobal.h

/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNLocalMinMaxFP16_Pack4.S
MNNAsmGlobal.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNAsmGlobal.h

/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNLocalMinMaxFP16_Pack8.S
MNNAsmGlobal.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNAsmGlobal.h

/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNPackC8FP16.S
MNNAsmGlobal.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNAsmGlobal.h

/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNPackedMatMulFP16.S
MNNAsmGlobal.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNAsmGlobal.h

/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNPackedMatMulRemainFP16.S
MNNAsmGlobal.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNAsmGlobal.h

/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNQuantizeFP16_UNIT4.S
MNNAsmGlobal.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNAsmGlobal.h

/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNUnpackC8FP16.S
MNNAsmGlobal.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNAsmGlobal.h

/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/sme2_asm/MNNPackedMatMulRemainFP16_SME2.S
MNNAsmGlobal.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/sme2_asm/MNNAsmGlobal.h

