#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

../3rd_party/flatbuffers/include/flatbuffers/base.h
assert.h
-
cstdint
-
cstddef
-
cstdlib
-
cstring
-
crtdbg.h
-
utility.h
-
utility
-
string
-
type_traits
-
vector
-
set
-
algorithm
-
iterator
-
memory
-
functional
-
stl_emulation.h
../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
string_view
-
experimental/string_view
-

../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
base.h
../3rd_party/flatbuffers/include/flatbuffers/base.h
cmath
-

../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
string
-
type_traits
-
vector
-
memory
-
limits
-
cctype
-

../3rd_party/half/half.hpp
utility
-
algorithm
-
iostream
-
limits
-
climits
-
cmath
-
cstring
-
cstdlib
-
type_traits
-
cstdint
-
functional
-

../include/MNN/AutoTime.hpp
stdint.h
-
stdio.h
-
MNN/MNNDefine.h
-

../include/MNN/ErrorCode.hpp

../include/MNN/HalideRuntime.h
stddef.h
-
stdint.h
-
stdbool.h
-

../include/MNN/MNNDefine.h
assert.h
-
stdio.h
-
TargetConditionals.h
-
hilog/log.h
-
android/log.h
-
syslog.h
-

../include/MNN/MNNForwardType.h
stdint.h
-
stddef.h
-

../include/MNN/Rect.h
math.h
-
algorithm
-
utility
-
MNN/MNNDefine.h
-

../include/MNN/Tensor.hpp
vector
-
MNN/HalideRuntime.h
-
MNN/MNNDefine.h
-

../schema/current/CaffeOp_generated.h
Tensor_generated.h
../schema/current/Tensor_generated.h
Type_generated.h
../schema/current/Type_generated.h

../schema/current/ExtraInfo_generated.h
Tensor_generated.h
../schema/current/Tensor_generated.h
Type_generated.h
../schema/current/Type_generated.h

../schema/current/MNN_generated.h
CaffeOp_generated.h
../schema/current/CaffeOp_generated.h
ExtraInfo_generated.h
../schema/current/ExtraInfo_generated.h
TFQuantizeOp_generated.h
../schema/current/TFQuantizeOp_generated.h
Tensor_generated.h
../schema/current/Tensor_generated.h
TensorflowOp_generated.h
../schema/current/TensorflowOp_generated.h
Type_generated.h
../schema/current/Type_generated.h
UserDefine_generated.h
../schema/current/UserDefine_generated.h

../schema/current/TFQuantizeOp_generated.h
CaffeOp_generated.h
../schema/current/CaffeOp_generated.h
Tensor_generated.h
../schema/current/Tensor_generated.h
Type_generated.h
../schema/current/Type_generated.h

../schema/current/Tensor_generated.h
Type_generated.h
../schema/current/Type_generated.h

../schema/current/TensorflowOp_generated.h
Tensor_generated.h
../schema/current/Tensor_generated.h
Type_generated.h
../schema/current/Type_generated.h

../schema/current/Type_generated.h
flatbuffers/flatbuffers.h
../schema/current/flatbuffers/flatbuffers.h

../schema/current/UserDefine_generated.h
Tensor_generated.h
../schema/current/Tensor_generated.h
Type_generated.h
../schema/current/Type_generated.h

../source/backend/arm82/Arm82Backend.hpp
backend/cpu/CPUBackend.hpp
../source/backend/arm82/backend/cpu/CPUBackend.hpp
core/Macro.h
../source/backend/arm82/core/Macro.h
core/TensorUtils.hpp
../source/backend/arm82/core/TensorUtils.hpp

../source/backend/arm82/Arm82Binary.hpp
core/Execution.hpp
../source/backend/arm82/core/Execution.hpp
backend/cpu/compute/CommonOptFunction.h
../source/backend/arm82/backend/cpu/compute/CommonOptFunction.h

../source/backend/cpu/BinaryUtils.hpp
math.h
-
algorithm
-
compute/CommonOptFunction.h
../source/backend/cpu/compute/CommonOptFunction.h
MNN_generated.h
../source/backend/cpu/MNN_generated.h

../source/backend/cpu/CPUBackend.hpp
map
-
memory
-
MNN/AutoTime.hpp
-
core/Backend.hpp
../source/backend/cpu/core/Backend.hpp
core/Execution.hpp
../source/backend/cpu/core/Execution.hpp
core/BufferAllocator.hpp
../source/backend/cpu/core/BufferAllocator.hpp
MNN_generated.h
../source/backend/cpu/MNN_generated.h
ThreadPool.hpp
../source/backend/cpu/ThreadPool.hpp

../source/backend/cpu/CPUPool.hpp
float.h
-
math.h
-
core/Macro.h
../source/backend/cpu/core/Macro.h
CaffeOp_generated.h
../source/backend/cpu/CaffeOp_generated.h

../source/backend/cpu/CPURaster.hpp
CPUBackend.hpp
../source/backend/cpu/CPUBackend.hpp
map
-
set
-
core/TensorUtils.hpp
../source/backend/cpu/core/TensorUtils.hpp
core/OpCommonUtils.hpp
../source/backend/cpu/core/OpCommonUtils.hpp

../source/backend/cpu/CPURuntime.hpp
stdint.h
-
vector
-
core/Macro.h
../source/backend/cpu/core/Macro.h

../source/backend/cpu/CPUTensorConvert.hpp
core/Execution.hpp
../source/backend/cpu/core/Execution.hpp
Tensor_generated.h
../source/backend/cpu/Tensor_generated.h
compute/CommonOptFunction.h
../source/backend/cpu/compute/CommonOptFunction.h

../source/backend/cpu/GridSampler.hpp

../source/backend/cpu/ThreadPool.hpp
condition_variable
-
functional
-
mutex
-
thread
-
vector
-
atomic
-
MNN/MNNDefine.h
-

../source/backend/cpu/UnaryUtils.hpp
cmath
-
vector
-
limits
-

../source/backend/cpu/compute/CommonOptFunction.h
stdint.h
-
stdio.h
-
string.h
-
vector
-
MNN/Rect.h
-
core/Macro.h
../source/backend/cpu/compute/core/Macro.h
backend/cpu/compute/Int8FunctionsOpt.h
../source/backend/cpu/compute/backend/cpu/compute/Int8FunctionsOpt.h

../source/backend/cpu/compute/Int8FunctionsOpt.h
stdint.h
-
stdio.h
-
sys/types.h
-
core/Macro.h
../source/backend/cpu/compute/core/Macro.h
core/ConvolutionCommon.hpp
../source/backend/cpu/compute/core/ConvolutionCommon.hpp
BaseTsd.h
-

../source/core/AutoStorage.h
stdint.h
-
string.h
-
MNNMemoryUtils.h
../source/core/MNNMemoryUtils.h

../source/core/Backend.hpp
MNN/MNNForwardType.h
-
MNN/ErrorCode.hpp
-
map
-
Command.hpp
../source/core/Command.hpp
NonCopyable.hpp
../source/core/NonCopyable.hpp
BufferAllocator.hpp
../source/core/BufferAllocator.hpp
future
-
atomic
-

../source/core/BufferAllocator.hpp
map
-
set
-
memory
-
vector
-
MNNMemoryUtils.h
../source/core/MNNMemoryUtils.h
NonCopyable.hpp
../source/core/NonCopyable.hpp
AutoStorage.h
../source/core/AutoStorage.h
MNN/Tensor.hpp
-
MNN/ErrorCode.hpp
-

../source/core/Command.hpp
MNN/Tensor.hpp
-
AutoStorage.h
../source/core/AutoStorage.h
string
-
memory
-

../source/core/Concurrency.h
backend/cpu/ThreadPool.hpp
../source/core/backend/cpu/ThreadPool.hpp
dispatch/dispatch.h
-
stddef.h
-
omp.h
-
omp.h
-

../source/core/ConvolutionCommon.hpp
AutoStorage.h
../source/core/AutoStorage.h
Execution.hpp
../source/core/Execution.hpp
MNN_generated.h
../source/core/MNN_generated.h

../source/core/Execution.hpp
MNN/MNNForwardType.h
-
MNN/ErrorCode.hpp
-
MNN/Tensor.hpp
-
memory
-
string
-
NonCopyable.hpp
../source/core/NonCopyable.hpp

../source/core/FileLoader.hpp
vector
-
mutex
-
string
-
core/AutoStorage.h
../source/core/core/AutoStorage.h

../source/core/MNNMemoryUtils.h
stdio.h
-
core/Macro.h
../source/core/core/Macro.h

../source/core/Macro.h
MNN/MNNDefine.h
-
BaseTsd.h
-

../source/core/NonCopyable.hpp

../source/core/OpCommonUtils.hpp
MNN/Tensor.hpp
-
TensorUtils.hpp
../source/core/TensorUtils.hpp
FileLoader.hpp
../source/core/FileLoader.hpp

../source/core/SimdHeader.h
arm_neon.h
-
intrin.h
-
smmintrin.h
-
x86intrin.h
-

../source/core/TensorUtils.hpp
MNN/Tensor.hpp
-
Backend.hpp
../source/core/Backend.hpp
AutoStorage.h
../source/core/AutoStorage.h
Tensor_generated.h
../source/core/Tensor_generated.h

../source/math/Vec.hpp
core/Macro.h
../source/math/core/Macro.h
core/SimdHeader.h
../source/math/core/SimdHeader.h
array
-
algorithm
-
math.h
-

/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Backend.cpp
half.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/half.hpp
algorithm
-
mutex
-
Arm82Backend.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Backend.hpp
Arm82OptFunc.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82OptFunc.hpp
Arm82Interp.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Interp.hpp
Arm82Functions.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Functions.hpp
core/BufferAllocator.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/core/BufferAllocator.hpp
core/TensorUtils.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/core/TensorUtils.hpp
core/OpCommonUtils.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/core/OpCommonUtils.hpp
backend/cpu/compute/CommonOptFunction.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/backend/cpu/compute/CommonOptFunction.h
backend/cpu/CPUTensorConvert.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/backend/cpu/CPUTensorConvert.hpp
backend/cpu/CPURaster.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/backend/cpu/CPURaster.hpp

/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Backend.hpp
backend/cpu/CPUBackend.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/backend/cpu/CPUBackend.hpp
core/Macro.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/core/Macro.h
core/TensorUtils.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/core/TensorUtils.hpp

/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Binary.cpp
algorithm
-
backend/arm82/Arm82Binary.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/backend/arm82/Arm82Binary.hpp
backend/arm82/Arm82Backend.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/backend/arm82/Arm82Backend.hpp
backend/cpu/BinaryUtils.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/backend/cpu/BinaryUtils.hpp
core/Macro.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/core/Macro.h
arm_neon.h
-

/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Binary.hpp
core/Execution.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/core/Execution.hpp
backend/cpu/compute/CommonOptFunction.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/backend/cpu/compute/CommonOptFunction.h

/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Functions.cpp
math.h
-
float.h
-
Arm82Functions.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Functions.hpp
Arm82OptFunc.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82OptFunc.hpp
Arm82WinogradOptFunc.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82WinogradOptFunc.hpp
Arm82Vec.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Vec.hpp
Arm82Binary.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Binary.hpp
Arm82Unary.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Unary.hpp
Arm82Relu.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Relu.hpp
backend/cpu/compute/CommonOptFunction.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/backend/cpu/compute/CommonOptFunction.h
backend/cpu/CPUPool.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/backend/cpu/CPUPool.hpp
backend/cpu/CPURuntime.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/backend/cpu/CPURuntime.hpp
backend/cpu/GridSampler.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/backend/cpu/GridSampler.hpp
arm_neon.h
-

/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Functions.hpp
stdint.h
-
stdio.h
-
string.h
-
core/Macro.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/core/Macro.h
backend/cpu/CPUBackend.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/backend/cpu/CPUBackend.hpp

/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Interp.cpp
Arm82Interp.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Interp.hpp
Arm82OptFunc.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82OptFunc.hpp
math.h
-
core/Concurrency.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/core/Concurrency.h
core/Macro.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/core/Macro.h
arm_neon.h
-

/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Interp.hpp
Arm82Backend.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Backend.hpp
core/AutoStorage.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/core/AutoStorage.h
core/Execution.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/core/Execution.hpp

/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82OptFunc.cpp
Arm82OptFunc.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82OptFunc.hpp
Arm82Vec.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Vec.hpp
core/Macro.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/core/Macro.h
half.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/half.hpp
arm_neon.h
-

/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82OptFunc.hpp
Arm82Backend.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Backend.hpp
core/Macro.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/core/Macro.h

/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Relu.cpp
limits
-
Arm82Relu.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Relu.hpp
Arm82Backend.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Backend.hpp
Arm82OptFunc.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82OptFunc.hpp
core/Concurrency.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/core/Concurrency.h
core/Macro.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/core/Macro.h
half.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/half.hpp
algorithm
-
arm_neon.h
-

/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Relu.hpp
stddef.h
-

/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Unary.cpp
vector
-
cmath
-
algorithm
-
Arm82Unary.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Unary.hpp
Arm82Backend.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Backend.hpp
core/Macro.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/core/Macro.h
core/OpCommonUtils.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/core/OpCommonUtils.hpp
core/Concurrency.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/core/Concurrency.h
backend/cpu/UnaryUtils.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/backend/cpu/UnaryUtils.hpp
Arm82OptFunc.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82OptFunc.hpp
MNN_generated.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/MNN_generated.h
arm_neon.h
-

/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Unary.hpp
core/Execution.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/core/Execution.hpp
backend/cpu/compute/CommonOptFunction.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/backend/cpu/compute/CommonOptFunction.h

/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Vec.hpp
Arm82Backend.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Backend.hpp
math/Vec.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/math/Vec.hpp

/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82WinogradOptFunc.cpp
Arm82WinogradOptFunc.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82WinogradOptFunc.hpp
Arm82Vec.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Vec.hpp
Arm82OptFunc.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82OptFunc.hpp
cstring
-
memory
-
map
-
core/Macro.h
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/core/Macro.h
math/Vec.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/math/Vec.hpp

/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82WinogradOptFunc.hpp
Arm82Backend.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Backend.hpp

