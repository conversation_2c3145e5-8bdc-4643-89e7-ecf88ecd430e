# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  "ASM"
  "CXX"
  )
# The set of files for implicit dependencies of each language:
set(CMAKE_DEPENDS_CHECK_ASM
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/Arm82MNNPackForMatMul_A.S" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/Arm82MNNPackForMatMul_A.S.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/CountMinMaxValue_FP16.S" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/CountMinMaxValue_FP16.S.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNConvRunForLineDepthwiseFP16.S" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNConvRunForLineDepthwiseFP16.S.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNConvWinoSourceTransformUnit6x6FP16.S" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNConvWinoSourceTransformUnit6x6FP16.S.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNDepthwiseConvFastKernelFP16.S" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNDepthwiseConvFastKernelFP16.S.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNExpFP16.S" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNExpFP16.S.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNGeluFP16.S" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNGeluFP16.S.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNLocalMinMaxFP16_Pack4.S" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNLocalMinMaxFP16_Pack4.S.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNLocalMinMaxFP16_Pack8.S" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNLocalMinMaxFP16_Pack8.S.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNPackC8FP16.S" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNPackC8FP16.S.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNPackedMatMulFP16.S" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNPackedMatMulFP16.S.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNPackedMatMulRemainFP16.S" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNPackedMatMulRemainFP16.S.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNQuantizeFP16_UNIT4.S" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNQuantizeFP16_UNIT4.S.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNUnpackC8FP16.S" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNUnpackC8FP16.S.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/sme2_asm/MNNPackedMatMulRemainFP16_SME2.S" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/sme2_asm/MNNPackedMatMulRemainFP16_SME2.S.o"
  )
set(CMAKE_ASM_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_ASM
  "MNN_KLEIDIAI_ENABLED=1"
  "MNN_SME2"
  "MNN_SUPPORT_DEPRECATED_OPV2"
  "MNN_SUPPORT_QUANT_EXTEND"
  "MNN_USE_NEON"
  "MNN_USE_THREAD_POOL"
  )

# The include file search paths:
set(CMAKE_ASM_TARGET_INCLUDE_PATH
  "../include"
  "../source"
  "../express"
  "../tools"
  "../codegen"
  "../schema/current"
  "../3rd_party"
  "../3rd_party/flatbuffers/include"
  "../3rd_party/half"
  "../3rd_party/imageHelper"
  "../3rd_party/OpenCLHeaders"
  "_deps/kleidiai-v1.9.0"
  "_deps/kleidiai-v1.9.0/kai"
  "_deps/kleidiai-v1.9.0/kai/ukernels"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/matmul_clamp_f32_qai8dxp_qsi4cxp"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/matmul_clamp_f16_qsi8d32p_qai4c32p"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/matmul_clamp_f32_qsi8d32p_qai4c32p"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/pack"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/matmul_clamp_f32_f32p_f32p"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/matmul_clamp_f32_f32_f32p"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/matmul_clamp_f16_f16p_f16p"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/matmul_clamp_f16_f16_f16p"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/imatmul_clamp_f32_f32p_f32p"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/imatmul_clamp_f16_f16p_f16p"
  "../source/backend/arm82"
  "../source/backend/arm82/compute"
  "../source/backend/arm82/asm"
  )
set(CMAKE_DEPENDS_CHECK_CXX
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Backend.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Binary.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Functions.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Interp.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82OptFunc.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Relu.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Unary.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o"
  "/root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82WinogradOptFunc.cpp" "/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o"
  )
set(CMAKE_CXX_COMPILER_ID "GNU")

# Preprocessor definitions for this target.
set(CMAKE_TARGET_DEFINITIONS_CXX
  "MNN_KLEIDIAI_ENABLED=1"
  "MNN_SME2"
  "MNN_SUPPORT_DEPRECATED_OPV2"
  "MNN_SUPPORT_QUANT_EXTEND"
  "MNN_USE_NEON"
  "MNN_USE_THREAD_POOL"
  )

# The include file search paths:
set(CMAKE_CXX_TARGET_INCLUDE_PATH
  "../include"
  "../source"
  "../express"
  "../tools"
  "../codegen"
  "../schema/current"
  "../3rd_party"
  "../3rd_party/flatbuffers/include"
  "../3rd_party/half"
  "../3rd_party/imageHelper"
  "../3rd_party/OpenCLHeaders"
  "_deps/kleidiai-v1.9.0"
  "_deps/kleidiai-v1.9.0/kai"
  "_deps/kleidiai-v1.9.0/kai/ukernels"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/matmul_clamp_f32_qai8dxp_qsi4cxp"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/matmul_clamp_f16_qsi8d32p_qai4c32p"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/matmul_clamp_f32_qsi8d32p_qai4c32p"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/pack"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/matmul_clamp_f32_f32p_f32p"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/matmul_clamp_f32_f32_f32p"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/matmul_clamp_f16_f16p_f16p"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/matmul_clamp_f16_f16_f16p"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/imatmul_clamp_f32_f32p_f32p"
  "_deps/kleidiai-v1.9.0/kai/ukernels/matmul/imatmul_clamp_f16_f16p_f16p"
  "../source/backend/arm82"
  "../source/backend/arm82/compute"
  "../source/backend/arm82/asm"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
