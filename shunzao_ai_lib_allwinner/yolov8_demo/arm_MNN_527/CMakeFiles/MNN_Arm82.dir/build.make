# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /root/pan/shunzao_ai_lib-develop/MNN-master

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm

# Include any dependencies generated for this target.
include CMakeFiles/MNN_Arm82.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/MNN_Arm82.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/MNN_Arm82.dir/flags.make

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: CMakeFiles/MNN_Arm82.dir/flags.make
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../source/backend/arm82/Arm82Backend.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -march=armv8.2-a+fp16 -o CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Backend.cpp

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -march=armv8.2-a+fp16 -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Backend.cpp > CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.i

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -march=armv8.2-a+fp16 -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Backend.cpp -o CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.s

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o: CMakeFiles/MNN_Arm82.dir/flags.make
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o: ../source/backend/arm82/Arm82Binary.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -march=armv8.2-a+fp16 -o CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Binary.cpp

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -march=armv8.2-a+fp16 -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Binary.cpp > CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.i

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -march=armv8.2-a+fp16 -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Binary.cpp -o CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.s

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: CMakeFiles/MNN_Arm82.dir/flags.make
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../source/backend/arm82/Arm82Functions.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -march=armv8.2-a+fp16 -o CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Functions.cpp

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -march=armv8.2-a+fp16 -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Functions.cpp > CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.i

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -march=armv8.2-a+fp16 -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Functions.cpp -o CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.s

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.o: CMakeFiles/MNN_Arm82.dir/flags.make
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.o: ../source/backend/arm82/Arm82Interp.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -march=armv8.2-a+fp16 -o CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Interp.cpp

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -march=armv8.2-a+fp16 -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Interp.cpp > CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.i

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -march=armv8.2-a+fp16 -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Interp.cpp -o CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.s

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o: CMakeFiles/MNN_Arm82.dir/flags.make
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o: ../source/backend/arm82/Arm82OptFunc.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -march=armv8.2-a+fp16 -o CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82OptFunc.cpp

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -march=armv8.2-a+fp16 -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82OptFunc.cpp > CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.i

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -march=armv8.2-a+fp16 -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82OptFunc.cpp -o CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.s

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.o: CMakeFiles/MNN_Arm82.dir/flags.make
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.o: ../source/backend/arm82/Arm82Relu.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -march=armv8.2-a+fp16 -o CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Relu.cpp

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -march=armv8.2-a+fp16 -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Relu.cpp > CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.i

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -march=armv8.2-a+fp16 -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Relu.cpp -o CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.s

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o: CMakeFiles/MNN_Arm82.dir/flags.make
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o: ../source/backend/arm82/Arm82Unary.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -march=armv8.2-a+fp16 -o CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Unary.cpp

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -march=armv8.2-a+fp16 -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Unary.cpp > CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.i

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -march=armv8.2-a+fp16 -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Unary.cpp -o CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.s

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o: CMakeFiles/MNN_Arm82.dir/flags.make
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o: ../source/backend/arm82/Arm82WinogradOptFunc.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -march=armv8.2-a+fp16 -o CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82WinogradOptFunc.cpp

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -march=armv8.2-a+fp16 -E /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82WinogradOptFunc.cpp > CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.i

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -march=armv8.2-a+fp16 -S /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82WinogradOptFunc.cpp -o CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.s

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/Arm82MNNPackForMatMul_A.S.o: CMakeFiles/MNN_Arm82.dir/flags.make
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/Arm82MNNPackForMatMul_A.S.o: ../source/backend/arm82/asm/arm64/Arm82MNNPackForMatMul_A.S
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building ASM object CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/Arm82MNNPackForMatMul_A.S.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-gcc $(ASM_DEFINES) $(ASM_INCLUDES) $(ASM_FLAGS) -march=armv8.2-a+fp16 -o CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/Arm82MNNPackForMatMul_A.S.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/Arm82MNNPackForMatMul_A.S

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/CountMinMaxValue_FP16.S.o: CMakeFiles/MNN_Arm82.dir/flags.make
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/CountMinMaxValue_FP16.S.o: ../source/backend/arm82/asm/arm64/CountMinMaxValue_FP16.S
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building ASM object CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/CountMinMaxValue_FP16.S.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-gcc $(ASM_DEFINES) $(ASM_INCLUDES) $(ASM_FLAGS) -march=armv8.2-a+fp16 -o CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/CountMinMaxValue_FP16.S.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/CountMinMaxValue_FP16.S

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNConvRunForLineDepthwiseFP16.S.o: CMakeFiles/MNN_Arm82.dir/flags.make
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNConvRunForLineDepthwiseFP16.S.o: ../source/backend/arm82/asm/arm64/MNNConvRunForLineDepthwiseFP16.S
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building ASM object CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNConvRunForLineDepthwiseFP16.S.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-gcc $(ASM_DEFINES) $(ASM_INCLUDES) $(ASM_FLAGS) -march=armv8.2-a+fp16 -o CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNConvRunForLineDepthwiseFP16.S.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNConvRunForLineDepthwiseFP16.S

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNConvWinoSourceTransformUnit6x6FP16.S.o: CMakeFiles/MNN_Arm82.dir/flags.make
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNConvWinoSourceTransformUnit6x6FP16.S.o: ../source/backend/arm82/asm/arm64/MNNConvWinoSourceTransformUnit6x6FP16.S
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building ASM object CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNConvWinoSourceTransformUnit6x6FP16.S.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-gcc $(ASM_DEFINES) $(ASM_INCLUDES) $(ASM_FLAGS) -march=armv8.2-a+fp16 -o CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNConvWinoSourceTransformUnit6x6FP16.S.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNConvWinoSourceTransformUnit6x6FP16.S

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNDepthwiseConvFastKernelFP16.S.o: CMakeFiles/MNN_Arm82.dir/flags.make
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNDepthwiseConvFastKernelFP16.S.o: ../source/backend/arm82/asm/arm64/MNNDepthwiseConvFastKernelFP16.S
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building ASM object CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNDepthwiseConvFastKernelFP16.S.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-gcc $(ASM_DEFINES) $(ASM_INCLUDES) $(ASM_FLAGS) -march=armv8.2-a+fp16 -o CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNDepthwiseConvFastKernelFP16.S.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNDepthwiseConvFastKernelFP16.S

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNExpFP16.S.o: CMakeFiles/MNN_Arm82.dir/flags.make
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNExpFP16.S.o: ../source/backend/arm82/asm/arm64/MNNExpFP16.S
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building ASM object CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNExpFP16.S.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-gcc $(ASM_DEFINES) $(ASM_INCLUDES) $(ASM_FLAGS) -march=armv8.2-a+fp16 -o CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNExpFP16.S.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNExpFP16.S

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNGeluFP16.S.o: CMakeFiles/MNN_Arm82.dir/flags.make
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNGeluFP16.S.o: ../source/backend/arm82/asm/arm64/MNNGeluFP16.S
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building ASM object CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNGeluFP16.S.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-gcc $(ASM_DEFINES) $(ASM_INCLUDES) $(ASM_FLAGS) -march=armv8.2-a+fp16 -o CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNGeluFP16.S.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNGeluFP16.S

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNLocalMinMaxFP16_Pack4.S.o: CMakeFiles/MNN_Arm82.dir/flags.make
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNLocalMinMaxFP16_Pack4.S.o: ../source/backend/arm82/asm/arm64/MNNLocalMinMaxFP16_Pack4.S
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building ASM object CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNLocalMinMaxFP16_Pack4.S.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-gcc $(ASM_DEFINES) $(ASM_INCLUDES) $(ASM_FLAGS) -march=armv8.2-a+fp16 -o CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNLocalMinMaxFP16_Pack4.S.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNLocalMinMaxFP16_Pack4.S

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNLocalMinMaxFP16_Pack8.S.o: CMakeFiles/MNN_Arm82.dir/flags.make
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNLocalMinMaxFP16_Pack8.S.o: ../source/backend/arm82/asm/arm64/MNNLocalMinMaxFP16_Pack8.S
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building ASM object CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNLocalMinMaxFP16_Pack8.S.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-gcc $(ASM_DEFINES) $(ASM_INCLUDES) $(ASM_FLAGS) -march=armv8.2-a+fp16 -o CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNLocalMinMaxFP16_Pack8.S.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNLocalMinMaxFP16_Pack8.S

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNPackC8FP16.S.o: CMakeFiles/MNN_Arm82.dir/flags.make
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNPackC8FP16.S.o: ../source/backend/arm82/asm/arm64/MNNPackC8FP16.S
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building ASM object CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNPackC8FP16.S.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-gcc $(ASM_DEFINES) $(ASM_INCLUDES) $(ASM_FLAGS) -march=armv8.2-a+fp16 -o CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNPackC8FP16.S.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNPackC8FP16.S

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNPackedMatMulFP16.S.o: CMakeFiles/MNN_Arm82.dir/flags.make
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNPackedMatMulFP16.S.o: ../source/backend/arm82/asm/arm64/MNNPackedMatMulFP16.S
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Building ASM object CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNPackedMatMulFP16.S.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-gcc $(ASM_DEFINES) $(ASM_INCLUDES) $(ASM_FLAGS) -march=armv8.2-a+fp16 -o CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNPackedMatMulFP16.S.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNPackedMatMulFP16.S

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNPackedMatMulRemainFP16.S.o: CMakeFiles/MNN_Arm82.dir/flags.make
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNPackedMatMulRemainFP16.S.o: ../source/backend/arm82/asm/arm64/MNNPackedMatMulRemainFP16.S
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Building ASM object CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNPackedMatMulRemainFP16.S.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-gcc $(ASM_DEFINES) $(ASM_INCLUDES) $(ASM_FLAGS) -march=armv8.2-a+fp16 -o CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNPackedMatMulRemainFP16.S.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNPackedMatMulRemainFP16.S

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNQuantizeFP16_UNIT4.S.o: CMakeFiles/MNN_Arm82.dir/flags.make
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNQuantizeFP16_UNIT4.S.o: ../source/backend/arm82/asm/arm64/MNNQuantizeFP16_UNIT4.S
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_21) "Building ASM object CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNQuantizeFP16_UNIT4.S.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-gcc $(ASM_DEFINES) $(ASM_INCLUDES) $(ASM_FLAGS) -march=armv8.2-a+fp16 -o CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNQuantizeFP16_UNIT4.S.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNQuantizeFP16_UNIT4.S

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNUnpackC8FP16.S.o: CMakeFiles/MNN_Arm82.dir/flags.make
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNUnpackC8FP16.S.o: ../source/backend/arm82/asm/arm64/MNNUnpackC8FP16.S
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_22) "Building ASM object CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNUnpackC8FP16.S.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-gcc $(ASM_DEFINES) $(ASM_INCLUDES) $(ASM_FLAGS) -march=armv8.2-a+fp16 -o CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNUnpackC8FP16.S.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNUnpackC8FP16.S

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/sme2_asm/MNNPackedMatMulRemainFP16_SME2.S.o: CMakeFiles/MNN_Arm82.dir/flags.make
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/sme2_asm/MNNPackedMatMulRemainFP16_SME2.S.o: ../source/backend/arm82/asm/arm64/sme2_asm/MNNPackedMatMulRemainFP16_SME2.S
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_23) "Building ASM object CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/sme2_asm/MNNPackedMatMulRemainFP16_SME2.S.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-gcc $(ASM_DEFINES) $(ASM_INCLUDES) $(ASM_FLAGS) -fno-tree-vectorize -march=armv8.2-a+fp16 -o CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/sme2_asm/MNNPackedMatMulRemainFP16_SME2.S.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/sme2_asm/MNNPackedMatMulRemainFP16_SME2.S

MNN_Arm82: CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o
MNN_Arm82: CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o
MNN_Arm82: CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o
MNN_Arm82: CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.o
MNN_Arm82: CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o
MNN_Arm82: CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.o
MNN_Arm82: CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o
MNN_Arm82: CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o
MNN_Arm82: CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/Arm82MNNPackForMatMul_A.S.o
MNN_Arm82: CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/CountMinMaxValue_FP16.S.o
MNN_Arm82: CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNConvRunForLineDepthwiseFP16.S.o
MNN_Arm82: CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNConvWinoSourceTransformUnit6x6FP16.S.o
MNN_Arm82: CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNDepthwiseConvFastKernelFP16.S.o
MNN_Arm82: CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNExpFP16.S.o
MNN_Arm82: CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNGeluFP16.S.o
MNN_Arm82: CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNLocalMinMaxFP16_Pack4.S.o
MNN_Arm82: CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNLocalMinMaxFP16_Pack8.S.o
MNN_Arm82: CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNPackC8FP16.S.o
MNN_Arm82: CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNPackedMatMulFP16.S.o
MNN_Arm82: CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNPackedMatMulRemainFP16.S.o
MNN_Arm82: CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNQuantizeFP16_UNIT4.S.o
MNN_Arm82: CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNUnpackC8FP16.S.o
MNN_Arm82: CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/sme2_asm/MNNPackedMatMulRemainFP16_SME2.S.o
MNN_Arm82: CMakeFiles/MNN_Arm82.dir/build.make

.PHONY : MNN_Arm82

# Rule to build all files generated by this target.
CMakeFiles/MNN_Arm82.dir/build: MNN_Arm82

.PHONY : CMakeFiles/MNN_Arm82.dir/build

CMakeFiles/MNN_Arm82.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/MNN_Arm82.dir/cmake_clean.cmake
.PHONY : CMakeFiles/MNN_Arm82.dir/clean

CMakeFiles/MNN_Arm82.dir/depend:
	cd /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /root/pan/shunzao_ai_lib-develop/MNN-master /root/pan/shunzao_ai_lib-develop/MNN-master /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/MNN_Arm82.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/MNN_Arm82.dir/depend

