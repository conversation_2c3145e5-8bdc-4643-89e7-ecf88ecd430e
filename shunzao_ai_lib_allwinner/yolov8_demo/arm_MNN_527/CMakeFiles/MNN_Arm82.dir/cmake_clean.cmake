file(REMOVE_RECURSE
  "CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o"
  "CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o"
  "CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o"
  "CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.o"
  "CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o"
  "CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.o"
  "CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o"
  "CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o"
  "CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/Arm82MNNPackForMatMul_A.S.o"
  "CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/CountMinMaxValue_FP16.S.o"
  "CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNConvRunForLineDepthwiseFP16.S.o"
  "CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNConvWinoSourceTransformUnit6x6FP16.S.o"
  "CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNDepthwiseConvFastKernelFP16.S.o"
  "CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNExpFP16.S.o"
  "CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNGeluFP16.S.o"
  "CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNLocalMinMaxFP16_Pack4.S.o"
  "CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNLocalMinMaxFP16_Pack8.S.o"
  "CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNPackC8FP16.S.o"
  "CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNPackedMatMulFP16.S.o"
  "CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNPackedMatMulRemainFP16.S.o"
  "CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNQuantizeFP16_UNIT4.S.o"
  "CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNUnpackC8FP16.S.o"
  "CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/sme2_asm/MNNPackedMatMulRemainFP16_SME2.S.o"
)

# Per-language clean rules from dependency scanning.
foreach(lang ASM CXX)
  include(CMakeFiles/MNN_Arm82.dir/cmake_clean_${lang}.cmake OPTIONAL)
endforeach()
