# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/Arm82MNNPackForMatMul_A.S.o
 ../source/backend/arm82/asm/MNNAsmGlobal.h
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/Arm82MNNPackForMatMul_A.S
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/CountMinMaxValue_FP16.S.o
 ../source/backend/arm82/asm/MNNAsmGlobal.h
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/CountMinMaxValue_FP16.S
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNConvRunForLineDepthwiseFP16.S.o
 ../source/backend/arm82/asm/MNNAsmGlobal.h
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNConvRunForLineDepthwiseFP16.S
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNConvWinoSourceTransformUnit6x6FP16.S.o
 ../source/backend/arm82/asm/MNNAsmGlobal.h
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNConvWinoSourceTransformUnit6x6FP16.S
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNDepthwiseConvFastKernelFP16.S.o
 ../source/backend/arm82/asm/MNNAsmGlobal.h
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNDepthwiseConvFastKernelFP16.S
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNExpFP16.S.o
 ../source/backend/arm82/asm/MNNAsmGlobal.h
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNExpFP16.S
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNGeluFP16.S.o
 ../source/backend/arm82/asm/MNNAsmGlobal.h
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNGeluFP16.S
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNLocalMinMaxFP16_Pack4.S.o
 ../source/backend/arm82/asm/MNNAsmGlobal.h
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNLocalMinMaxFP16_Pack4.S
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNLocalMinMaxFP16_Pack8.S.o
 ../source/backend/arm82/asm/MNNAsmGlobal.h
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNLocalMinMaxFP16_Pack8.S
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNPackC8FP16.S.o
 ../source/backend/arm82/asm/MNNAsmGlobal.h
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNPackC8FP16.S
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNPackedMatMulFP16.S.o
 ../source/backend/arm82/asm/MNNAsmGlobal.h
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNPackedMatMulFP16.S
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNPackedMatMulRemainFP16.S.o
 ../source/backend/arm82/asm/MNNAsmGlobal.h
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNPackedMatMulRemainFP16.S
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNQuantizeFP16_UNIT4.S.o
 ../source/backend/arm82/asm/MNNAsmGlobal.h
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNQuantizeFP16_UNIT4.S
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNUnpackC8FP16.S.o
 ../source/backend/arm82/asm/MNNAsmGlobal.h
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/MNNUnpackC8FP16.S
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/sme2_asm/MNNPackedMatMulRemainFP16_SME2.S.o
 ../source/backend/arm82/asm/MNNAsmGlobal.h
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/asm/arm64/sme2_asm/MNNPackedMatMulRemainFP16_SME2.S
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o
 ../3rd_party/flatbuffers/include/flatbuffers/base.h
 ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
 ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
 ../3rd_party/half/half.hpp
 ../include/MNN/AutoTime.hpp
 ../include/MNN/ErrorCode.hpp
 ../include/MNN/HalideRuntime.h
 ../include/MNN/MNNDefine.h
 ../include/MNN/MNNForwardType.h
 ../include/MNN/Rect.h
 ../include/MNN/Tensor.hpp
 ../schema/current/CaffeOp_generated.h
 ../schema/current/ExtraInfo_generated.h
 ../schema/current/MNN_generated.h
 ../schema/current/TFQuantizeOp_generated.h
 ../schema/current/Tensor_generated.h
 ../schema/current/TensorflowOp_generated.h
 ../schema/current/Type_generated.h
 ../schema/current/UserDefine_generated.h
 ../source/backend/cpu/CPUBackend.hpp
 ../source/backend/cpu/CPURaster.hpp
 ../source/backend/cpu/CPUTensorConvert.hpp
 ../source/backend/cpu/ThreadPool.hpp
 ../source/backend/cpu/compute/CommonOptFunction.h
 ../source/backend/cpu/compute/Int8FunctionsOpt.h
 ../source/core/AutoStorage.h
 ../source/core/Backend.hpp
 ../source/core/BufferAllocator.hpp
 ../source/core/Command.hpp
 ../source/core/ConvolutionCommon.hpp
 ../source/core/Execution.hpp
 ../source/core/FileLoader.hpp
 ../source/core/MNNMemoryUtils.h
 ../source/core/Macro.h
 ../source/core/NonCopyable.hpp
 ../source/core/OpCommonUtils.hpp
 ../source/core/TensorUtils.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Backend.cpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Backend.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Functions.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Interp.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82OptFunc.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o
 ../3rd_party/flatbuffers/include/flatbuffers/base.h
 ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
 ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
 ../include/MNN/AutoTime.hpp
 ../include/MNN/ErrorCode.hpp
 ../include/MNN/HalideRuntime.h
 ../include/MNN/MNNDefine.h
 ../include/MNN/MNNForwardType.h
 ../include/MNN/Rect.h
 ../include/MNN/Tensor.hpp
 ../schema/current/CaffeOp_generated.h
 ../schema/current/ExtraInfo_generated.h
 ../schema/current/MNN_generated.h
 ../schema/current/TFQuantizeOp_generated.h
 ../schema/current/Tensor_generated.h
 ../schema/current/TensorflowOp_generated.h
 ../schema/current/Type_generated.h
 ../schema/current/UserDefine_generated.h
 ../source/backend/arm82/Arm82Backend.hpp
 ../source/backend/arm82/Arm82Binary.hpp
 ../source/backend/cpu/BinaryUtils.hpp
 ../source/backend/cpu/CPUBackend.hpp
 ../source/backend/cpu/ThreadPool.hpp
 ../source/backend/cpu/compute/CommonOptFunction.h
 ../source/backend/cpu/compute/Int8FunctionsOpt.h
 ../source/core/AutoStorage.h
 ../source/core/Backend.hpp
 ../source/core/BufferAllocator.hpp
 ../source/core/Command.hpp
 ../source/core/ConvolutionCommon.hpp
 ../source/core/Execution.hpp
 ../source/core/MNNMemoryUtils.h
 ../source/core/Macro.h
 ../source/core/NonCopyable.hpp
 ../source/core/TensorUtils.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Binary.cpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o
 ../3rd_party/flatbuffers/include/flatbuffers/base.h
 ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
 ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
 ../include/MNN/AutoTime.hpp
 ../include/MNN/ErrorCode.hpp
 ../include/MNN/HalideRuntime.h
 ../include/MNN/MNNDefine.h
 ../include/MNN/MNNForwardType.h
 ../include/MNN/Rect.h
 ../include/MNN/Tensor.hpp
 ../schema/current/CaffeOp_generated.h
 ../schema/current/ExtraInfo_generated.h
 ../schema/current/MNN_generated.h
 ../schema/current/TFQuantizeOp_generated.h
 ../schema/current/Tensor_generated.h
 ../schema/current/TensorflowOp_generated.h
 ../schema/current/Type_generated.h
 ../schema/current/UserDefine_generated.h
 ../source/backend/cpu/CPUBackend.hpp
 ../source/backend/cpu/CPUPool.hpp
 ../source/backend/cpu/CPURuntime.hpp
 ../source/backend/cpu/GridSampler.hpp
 ../source/backend/cpu/ThreadPool.hpp
 ../source/backend/cpu/compute/CommonOptFunction.h
 ../source/backend/cpu/compute/Int8FunctionsOpt.h
 ../source/core/AutoStorage.h
 ../source/core/Backend.hpp
 ../source/core/BufferAllocator.hpp
 ../source/core/Command.hpp
 ../source/core/ConvolutionCommon.hpp
 ../source/core/Execution.hpp
 ../source/core/MNNMemoryUtils.h
 ../source/core/Macro.h
 ../source/core/NonCopyable.hpp
 ../source/core/SimdHeader.h
 ../source/core/TensorUtils.hpp
 ../source/math/Vec.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Backend.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Binary.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Functions.cpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Functions.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82OptFunc.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Relu.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Unary.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Vec.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82WinogradOptFunc.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.o
 ../3rd_party/flatbuffers/include/flatbuffers/base.h
 ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
 ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
 ../include/MNN/AutoTime.hpp
 ../include/MNN/ErrorCode.hpp
 ../include/MNN/HalideRuntime.h
 ../include/MNN/MNNDefine.h
 ../include/MNN/MNNForwardType.h
 ../include/MNN/Tensor.hpp
 ../schema/current/CaffeOp_generated.h
 ../schema/current/ExtraInfo_generated.h
 ../schema/current/MNN_generated.h
 ../schema/current/TFQuantizeOp_generated.h
 ../schema/current/Tensor_generated.h
 ../schema/current/TensorflowOp_generated.h
 ../schema/current/Type_generated.h
 ../schema/current/UserDefine_generated.h
 ../source/backend/cpu/CPUBackend.hpp
 ../source/backend/cpu/ThreadPool.hpp
 ../source/core/AutoStorage.h
 ../source/core/Backend.hpp
 ../source/core/BufferAllocator.hpp
 ../source/core/Command.hpp
 ../source/core/Concurrency.h
 ../source/core/Execution.hpp
 ../source/core/MNNMemoryUtils.h
 ../source/core/Macro.h
 ../source/core/NonCopyable.hpp
 ../source/core/TensorUtils.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Backend.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Interp.cpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Interp.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82OptFunc.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o
 ../3rd_party/flatbuffers/include/flatbuffers/base.h
 ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
 ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
 ../3rd_party/half/half.hpp
 ../include/MNN/AutoTime.hpp
 ../include/MNN/ErrorCode.hpp
 ../include/MNN/HalideRuntime.h
 ../include/MNN/MNNDefine.h
 ../include/MNN/MNNForwardType.h
 ../include/MNN/Tensor.hpp
 ../schema/current/CaffeOp_generated.h
 ../schema/current/ExtraInfo_generated.h
 ../schema/current/MNN_generated.h
 ../schema/current/TFQuantizeOp_generated.h
 ../schema/current/Tensor_generated.h
 ../schema/current/TensorflowOp_generated.h
 ../schema/current/Type_generated.h
 ../schema/current/UserDefine_generated.h
 ../source/backend/cpu/CPUBackend.hpp
 ../source/backend/cpu/ThreadPool.hpp
 ../source/core/AutoStorage.h
 ../source/core/Backend.hpp
 ../source/core/BufferAllocator.hpp
 ../source/core/Command.hpp
 ../source/core/Execution.hpp
 ../source/core/MNNMemoryUtils.h
 ../source/core/Macro.h
 ../source/core/NonCopyable.hpp
 ../source/core/SimdHeader.h
 ../source/core/TensorUtils.hpp
 ../source/math/Vec.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Backend.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82OptFunc.cpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82OptFunc.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Vec.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.o
 ../3rd_party/flatbuffers/include/flatbuffers/base.h
 ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
 ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
 ../3rd_party/half/half.hpp
 ../include/MNN/AutoTime.hpp
 ../include/MNN/ErrorCode.hpp
 ../include/MNN/HalideRuntime.h
 ../include/MNN/MNNDefine.h
 ../include/MNN/MNNForwardType.h
 ../include/MNN/Tensor.hpp
 ../schema/current/CaffeOp_generated.h
 ../schema/current/ExtraInfo_generated.h
 ../schema/current/MNN_generated.h
 ../schema/current/TFQuantizeOp_generated.h
 ../schema/current/Tensor_generated.h
 ../schema/current/TensorflowOp_generated.h
 ../schema/current/Type_generated.h
 ../schema/current/UserDefine_generated.h
 ../source/backend/cpu/CPUBackend.hpp
 ../source/backend/cpu/ThreadPool.hpp
 ../source/core/AutoStorage.h
 ../source/core/Backend.hpp
 ../source/core/BufferAllocator.hpp
 ../source/core/Command.hpp
 ../source/core/Concurrency.h
 ../source/core/Execution.hpp
 ../source/core/MNNMemoryUtils.h
 ../source/core/Macro.h
 ../source/core/NonCopyable.hpp
 ../source/core/TensorUtils.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Backend.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82OptFunc.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Relu.cpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Relu.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o
 ../3rd_party/flatbuffers/include/flatbuffers/base.h
 ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
 ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
 ../include/MNN/AutoTime.hpp
 ../include/MNN/ErrorCode.hpp
 ../include/MNN/HalideRuntime.h
 ../include/MNN/MNNDefine.h
 ../include/MNN/MNNForwardType.h
 ../include/MNN/Rect.h
 ../include/MNN/Tensor.hpp
 ../schema/current/CaffeOp_generated.h
 ../schema/current/ExtraInfo_generated.h
 ../schema/current/MNN_generated.h
 ../schema/current/TFQuantizeOp_generated.h
 ../schema/current/Tensor_generated.h
 ../schema/current/TensorflowOp_generated.h
 ../schema/current/Type_generated.h
 ../schema/current/UserDefine_generated.h
 ../source/backend/cpu/CPUBackend.hpp
 ../source/backend/cpu/ThreadPool.hpp
 ../source/backend/cpu/UnaryUtils.hpp
 ../source/backend/cpu/compute/CommonOptFunction.h
 ../source/backend/cpu/compute/Int8FunctionsOpt.h
 ../source/core/AutoStorage.h
 ../source/core/Backend.hpp
 ../source/core/BufferAllocator.hpp
 ../source/core/Command.hpp
 ../source/core/Concurrency.h
 ../source/core/ConvolutionCommon.hpp
 ../source/core/Execution.hpp
 ../source/core/FileLoader.hpp
 ../source/core/MNNMemoryUtils.h
 ../source/core/Macro.h
 ../source/core/NonCopyable.hpp
 ../source/core/OpCommonUtils.hpp
 ../source/core/TensorUtils.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Backend.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82OptFunc.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Unary.cpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Unary.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o
 ../3rd_party/flatbuffers/include/flatbuffers/base.h
 ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
 ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
 ../include/MNN/AutoTime.hpp
 ../include/MNN/ErrorCode.hpp
 ../include/MNN/HalideRuntime.h
 ../include/MNN/MNNDefine.h
 ../include/MNN/MNNForwardType.h
 ../include/MNN/Tensor.hpp
 ../schema/current/CaffeOp_generated.h
 ../schema/current/ExtraInfo_generated.h
 ../schema/current/MNN_generated.h
 ../schema/current/TFQuantizeOp_generated.h
 ../schema/current/Tensor_generated.h
 ../schema/current/TensorflowOp_generated.h
 ../schema/current/Type_generated.h
 ../schema/current/UserDefine_generated.h
 ../source/backend/cpu/CPUBackend.hpp
 ../source/backend/cpu/ThreadPool.hpp
 ../source/core/AutoStorage.h
 ../source/core/Backend.hpp
 ../source/core/BufferAllocator.hpp
 ../source/core/Command.hpp
 ../source/core/Execution.hpp
 ../source/core/MNNMemoryUtils.h
 ../source/core/Macro.h
 ../source/core/NonCopyable.hpp
 ../source/core/SimdHeader.h
 ../source/core/TensorUtils.hpp
 ../source/math/Vec.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Backend.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82OptFunc.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82Vec.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82WinogradOptFunc.cpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/source/backend/arm82/Arm82WinogradOptFunc.hpp
