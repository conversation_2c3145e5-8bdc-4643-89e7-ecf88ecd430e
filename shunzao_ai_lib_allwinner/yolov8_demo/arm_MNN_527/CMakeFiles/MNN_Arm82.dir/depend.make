# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/Arm82MNNPackForMatMul_A.S.o: ../source/backend/arm82/asm/MNNAsmGlobal.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/Arm82MNNPackForMatMul_A.S.o: ../source/backend/arm82/asm/arm64/Arm82MNNPackForMatMul_A.S

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/CountMinMaxValue_FP16.S.o: ../source/backend/arm82/asm/MNNAsmGlobal.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/CountMinMaxValue_FP16.S.o: ../source/backend/arm82/asm/arm64/CountMinMaxValue_FP16.S

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNConvRunForLineDepthwiseFP16.S.o: ../source/backend/arm82/asm/MNNAsmGlobal.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNConvRunForLineDepthwiseFP16.S.o: ../source/backend/arm82/asm/arm64/MNNConvRunForLineDepthwiseFP16.S

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNConvWinoSourceTransformUnit6x6FP16.S.o: ../source/backend/arm82/asm/MNNAsmGlobal.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNConvWinoSourceTransformUnit6x6FP16.S.o: ../source/backend/arm82/asm/arm64/MNNConvWinoSourceTransformUnit6x6FP16.S

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNDepthwiseConvFastKernelFP16.S.o: ../source/backend/arm82/asm/MNNAsmGlobal.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNDepthwiseConvFastKernelFP16.S.o: ../source/backend/arm82/asm/arm64/MNNDepthwiseConvFastKernelFP16.S

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNExpFP16.S.o: ../source/backend/arm82/asm/MNNAsmGlobal.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNExpFP16.S.o: ../source/backend/arm82/asm/arm64/MNNExpFP16.S

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNGeluFP16.S.o: ../source/backend/arm82/asm/MNNAsmGlobal.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNGeluFP16.S.o: ../source/backend/arm82/asm/arm64/MNNGeluFP16.S

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNLocalMinMaxFP16_Pack4.S.o: ../source/backend/arm82/asm/MNNAsmGlobal.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNLocalMinMaxFP16_Pack4.S.o: ../source/backend/arm82/asm/arm64/MNNLocalMinMaxFP16_Pack4.S

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNLocalMinMaxFP16_Pack8.S.o: ../source/backend/arm82/asm/MNNAsmGlobal.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNLocalMinMaxFP16_Pack8.S.o: ../source/backend/arm82/asm/arm64/MNNLocalMinMaxFP16_Pack8.S

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNPackC8FP16.S.o: ../source/backend/arm82/asm/MNNAsmGlobal.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNPackC8FP16.S.o: ../source/backend/arm82/asm/arm64/MNNPackC8FP16.S

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNPackedMatMulFP16.S.o: ../source/backend/arm82/asm/MNNAsmGlobal.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNPackedMatMulFP16.S.o: ../source/backend/arm82/asm/arm64/MNNPackedMatMulFP16.S

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNPackedMatMulRemainFP16.S.o: ../source/backend/arm82/asm/MNNAsmGlobal.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNPackedMatMulRemainFP16.S.o: ../source/backend/arm82/asm/arm64/MNNPackedMatMulRemainFP16.S

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNQuantizeFP16_UNIT4.S.o: ../source/backend/arm82/asm/MNNAsmGlobal.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNQuantizeFP16_UNIT4.S.o: ../source/backend/arm82/asm/arm64/MNNQuantizeFP16_UNIT4.S

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNUnpackC8FP16.S.o: ../source/backend/arm82/asm/MNNAsmGlobal.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/MNNUnpackC8FP16.S.o: ../source/backend/arm82/asm/arm64/MNNUnpackC8FP16.S

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/sme2_asm/MNNPackedMatMulRemainFP16_SME2.S.o: ../source/backend/arm82/asm/MNNAsmGlobal.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/asm/arm64/sme2_asm/MNNPackedMatMulRemainFP16_SME2.S.o: ../source/backend/arm82/asm/arm64/sme2_asm/MNNPackedMatMulRemainFP16_SME2.S

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../3rd_party/half/half.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../include/MNN/AutoTime.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../include/MNN/Rect.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../source/backend/cpu/CPUBackend.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../source/backend/cpu/CPURaster.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../source/backend/cpu/CPUTensorConvert.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../source/backend/cpu/ThreadPool.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../source/backend/cpu/compute/CommonOptFunction.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../source/backend/cpu/compute/Int8FunctionsOpt.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../source/core/ConvolutionCommon.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../source/core/Macro.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../source/core/OpCommonUtils.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../source/backend/arm82/Arm82Backend.cpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../source/backend/arm82/Arm82Backend.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../source/backend/arm82/Arm82Functions.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../source/backend/arm82/Arm82Interp.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Backend.cpp.o: ../source/backend/arm82/Arm82OptFunc.hpp

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o: ../include/MNN/AutoTime.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o: ../include/MNN/Rect.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o: ../source/backend/arm82/Arm82Backend.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o: ../source/backend/arm82/Arm82Binary.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o: ../source/backend/cpu/BinaryUtils.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o: ../source/backend/cpu/CPUBackend.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o: ../source/backend/cpu/ThreadPool.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o: ../source/backend/cpu/compute/CommonOptFunction.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o: ../source/backend/cpu/compute/Int8FunctionsOpt.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o: ../source/core/ConvolutionCommon.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o: ../source/core/Macro.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Binary.cpp.o: ../source/backend/arm82/Arm82Binary.cpp

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../include/MNN/AutoTime.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../include/MNN/Rect.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../source/backend/cpu/CPUBackend.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../source/backend/cpu/CPUPool.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../source/backend/cpu/CPURuntime.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../source/backend/cpu/GridSampler.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../source/backend/cpu/ThreadPool.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../source/backend/cpu/compute/CommonOptFunction.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../source/backend/cpu/compute/Int8FunctionsOpt.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../source/core/ConvolutionCommon.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../source/core/Macro.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../source/core/SimdHeader.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../source/math/Vec.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../source/backend/arm82/Arm82Backend.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../source/backend/arm82/Arm82Binary.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../source/backend/arm82/Arm82Functions.cpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../source/backend/arm82/Arm82Functions.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../source/backend/arm82/Arm82OptFunc.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../source/backend/arm82/Arm82Relu.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../source/backend/arm82/Arm82Unary.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../source/backend/arm82/Arm82Vec.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Functions.cpp.o: ../source/backend/arm82/Arm82WinogradOptFunc.hpp

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.o: ../include/MNN/AutoTime.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.o: ../source/backend/cpu/CPUBackend.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.o: ../source/backend/cpu/ThreadPool.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.o: ../source/core/Concurrency.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.o: ../source/core/Macro.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.o: ../source/backend/arm82/Arm82Backend.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.o: ../source/backend/arm82/Arm82Interp.cpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.o: ../source/backend/arm82/Arm82Interp.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Interp.cpp.o: ../source/backend/arm82/Arm82OptFunc.hpp

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o: ../3rd_party/half/half.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o: ../include/MNN/AutoTime.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o: ../source/backend/cpu/CPUBackend.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o: ../source/backend/cpu/ThreadPool.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o: ../source/core/Macro.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o: ../source/core/SimdHeader.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o: ../source/math/Vec.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o: ../source/backend/arm82/Arm82Backend.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o: ../source/backend/arm82/Arm82OptFunc.cpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o: ../source/backend/arm82/Arm82OptFunc.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82OptFunc.cpp.o: ../source/backend/arm82/Arm82Vec.hpp

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.o: ../3rd_party/half/half.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.o: ../include/MNN/AutoTime.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.o: ../source/backend/cpu/CPUBackend.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.o: ../source/backend/cpu/ThreadPool.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.o: ../source/core/Concurrency.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.o: ../source/core/Macro.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.o: ../source/backend/arm82/Arm82Backend.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.o: ../source/backend/arm82/Arm82OptFunc.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.o: ../source/backend/arm82/Arm82Relu.cpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Relu.cpp.o: ../source/backend/arm82/Arm82Relu.hpp

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o: ../include/MNN/AutoTime.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o: ../include/MNN/Rect.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o: ../source/backend/cpu/CPUBackend.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o: ../source/backend/cpu/ThreadPool.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o: ../source/backend/cpu/UnaryUtils.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o: ../source/backend/cpu/compute/CommonOptFunction.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o: ../source/backend/cpu/compute/Int8FunctionsOpt.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o: ../source/core/Concurrency.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o: ../source/core/ConvolutionCommon.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o: ../source/core/FileLoader.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o: ../source/core/Macro.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o: ../source/core/OpCommonUtils.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o: ../source/backend/arm82/Arm82Backend.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o: ../source/backend/arm82/Arm82OptFunc.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o: ../source/backend/arm82/Arm82Unary.cpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82Unary.cpp.o: ../source/backend/arm82/Arm82Unary.hpp

CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o: ../include/MNN/AutoTime.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o: ../source/backend/cpu/CPUBackend.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o: ../source/backend/cpu/ThreadPool.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o: ../source/core/Backend.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o: ../source/core/Command.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o: ../source/core/Execution.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o: ../source/core/Macro.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o: ../source/core/SimdHeader.h
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o: ../source/math/Vec.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o: ../source/backend/arm82/Arm82Backend.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o: ../source/backend/arm82/Arm82OptFunc.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o: ../source/backend/arm82/Arm82Vec.hpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o: ../source/backend/arm82/Arm82WinogradOptFunc.cpp
CMakeFiles/MNN_Arm82.dir/source/backend/arm82/Arm82WinogradOptFunc.cpp.o: ../source/backend/arm82/Arm82WinogradOptFunc.hpp

