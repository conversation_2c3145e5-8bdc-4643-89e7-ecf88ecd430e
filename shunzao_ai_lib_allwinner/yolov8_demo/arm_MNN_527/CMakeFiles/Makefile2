# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /root/pan/shunzao_ai_lib-develop/MNN-master

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/OpenCLProgramBuildTest.out.dir/all
all: CMakeFiles/MNNUtils.dir/all
all: CMakeFiles/mobilenetTest.out.dir/all
all: CMakeFiles/MNNV2Basic.out.dir/all
all: CMakeFiles/checkInvalidValue.out.dir/all
all: CMakeFiles/MNN.dir/all
all: CMakeFiles/ModuleBasic.out.dir/all
all: CMakeFiles/GetMNNInfo.dir/all
all: CMakeFiles/MNN_Arm82.dir/all
all: CMakeFiles/MNNARM64.dir/all
all: CMakeFiles/backendTest.out.dir/all
all: CMakeFiles/getPerformance.out.dir/all
all: CMakeFiles/checkDir.out.dir/all
all: CMakeFiles/MNNCPU.dir/all
all: CMakeFiles/MNNCV.dir/all
all: CMakeFiles/fuseTest.dir/all
all: CMakeFiles/MNNMath.dir/all
all: CMakeFiles/MNNCore.dir/all
all: CMakeFiles/MNNTransform.dir/all
all: CMakeFiles/SequenceModuleTest.out.dir/all
all: CMakeFiles/testModel_expr.out.dir/all
all: CMakeFiles/modelCompare.out.dir/all
all: CMakeFiles/testModel.out.dir/all
all: CMakeFiles/winogradExample.out.dir/all
all: CMakeFiles/timeProfile.out.dir/all
all: CMakeFiles/mergeInplaceForCPU.dir/all
all: CMakeFiles/testTrain.out.dir/all
all: CMakeFiles/testModelWithDescribe.out.dir/all
all: CMakeFiles/checkFile.out.dir/all
all: express/all
all: tools/converter/all
all: tools/audio/all

.PHONY : all

# The main recursive "preinstall" target.
preinstall: express/preinstall
preinstall: tools/converter/preinstall
preinstall: tools/audio/preinstall

.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/OpenCLProgramBuildTest.out.dir/clean
clean: CMakeFiles/MNNUtils.dir/clean
clean: CMakeFiles/mobilenetTest.out.dir/clean
clean: CMakeFiles/MNNV2Basic.out.dir/clean
clean: CMakeFiles/checkInvalidValue.out.dir/clean
clean: CMakeFiles/MNN.dir/clean
clean: CMakeFiles/ModuleBasic.out.dir/clean
clean: CMakeFiles/GetMNNInfo.dir/clean
clean: CMakeFiles/MNN_Arm82.dir/clean
clean: CMakeFiles/MNNARM64.dir/clean
clean: CMakeFiles/backendTest.out.dir/clean
clean: CMakeFiles/getPerformance.out.dir/clean
clean: CMakeFiles/checkDir.out.dir/clean
clean: CMakeFiles/MNNCPU.dir/clean
clean: CMakeFiles/MNNCV.dir/clean
clean: CMakeFiles/fuseTest.dir/clean
clean: CMakeFiles/MNNMath.dir/clean
clean: CMakeFiles/MNNCore.dir/clean
clean: CMakeFiles/MNNTransform.dir/clean
clean: CMakeFiles/SequenceModuleTest.out.dir/clean
clean: CMakeFiles/testModel_expr.out.dir/clean
clean: CMakeFiles/modelCompare.out.dir/clean
clean: CMakeFiles/testModel.out.dir/clean
clean: CMakeFiles/winogradExample.out.dir/clean
clean: CMakeFiles/timeProfile.out.dir/clean
clean: CMakeFiles/mergeInplaceForCPU.dir/clean
clean: CMakeFiles/testTrain.out.dir/clean
clean: CMakeFiles/testModelWithDescribe.out.dir/clean
clean: CMakeFiles/checkFile.out.dir/clean
clean: express/clean
clean: tools/converter/clean
clean: tools/audio/clean

.PHONY : clean

#=============================================================================
# Directory level rules for directory express

# Recursive "all" directory target.
express/all: express/CMakeFiles/MNN_Express.dir/all

.PHONY : express/all

# Recursive "preinstall" directory target.
express/preinstall:

.PHONY : express/preinstall

# Recursive "clean" directory target.
express/clean: express/CMakeFiles/MNN_Express.dir/clean

.PHONY : express/clean

#=============================================================================
# Directory level rules for directory tools/audio

# Recursive "all" directory target.
tools/audio/all:

.PHONY : tools/audio/all

# Recursive "preinstall" directory target.
tools/audio/preinstall:

.PHONY : tools/audio/preinstall

# Recursive "clean" directory target.
tools/audio/clean:

.PHONY : tools/audio/clean

#=============================================================================
# Directory level rules for directory tools/converter

# Recursive "all" directory target.
tools/converter/all:

.PHONY : tools/converter/all

# Recursive "preinstall" directory target.
tools/converter/preinstall:

.PHONY : tools/converter/preinstall

# Recursive "clean" directory target.
tools/converter/clean:

.PHONY : tools/converter/clean

#=============================================================================
# Target rules for target CMakeFiles/OpenCLProgramBuildTest.out.dir

# All Build rule for target.
CMakeFiles/OpenCLProgramBuildTest.out.dir/all: CMakeFiles/MNN.dir/all
CMakeFiles/OpenCLProgramBuildTest.out.dir/all: express/CMakeFiles/MNN_Express.dir/all
	$(MAKE) -f CMakeFiles/OpenCLProgramBuildTest.out.dir/build.make CMakeFiles/OpenCLProgramBuildTest.out.dir/depend
	$(MAKE) -f CMakeFiles/OpenCLProgramBuildTest.out.dir/build.make CMakeFiles/OpenCLProgramBuildTest.out.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=93 "Built target OpenCLProgramBuildTest.out"
.PHONY : CMakeFiles/OpenCLProgramBuildTest.out.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/OpenCLProgramBuildTest.out.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 93
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/OpenCLProgramBuildTest.out.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 0
.PHONY : CMakeFiles/OpenCLProgramBuildTest.out.dir/rule

# Convenience name for target.
OpenCLProgramBuildTest.out: CMakeFiles/OpenCLProgramBuildTest.out.dir/rule

.PHONY : OpenCLProgramBuildTest.out

# clean rule for target.
CMakeFiles/OpenCLProgramBuildTest.out.dir/clean:
	$(MAKE) -f CMakeFiles/OpenCLProgramBuildTest.out.dir/build.make CMakeFiles/OpenCLProgramBuildTest.out.dir/clean
.PHONY : CMakeFiles/OpenCLProgramBuildTest.out.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/MNNUtils.dir

# All Build rule for target.
CMakeFiles/MNNUtils.dir/all:
	$(MAKE) -f CMakeFiles/MNNUtils.dir/build.make CMakeFiles/MNNUtils.dir/depend
	$(MAKE) -f CMakeFiles/MNNUtils.dir/build.make CMakeFiles/MNNUtils.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=84 "Built target MNNUtils"
.PHONY : CMakeFiles/MNNUtils.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/MNNUtils.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 1
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/MNNUtils.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 0
.PHONY : CMakeFiles/MNNUtils.dir/rule

# Convenience name for target.
MNNUtils: CMakeFiles/MNNUtils.dir/rule

.PHONY : MNNUtils

# clean rule for target.
CMakeFiles/MNNUtils.dir/clean:
	$(MAKE) -f CMakeFiles/MNNUtils.dir/build.make CMakeFiles/MNNUtils.dir/clean
.PHONY : CMakeFiles/MNNUtils.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/mobilenetTest.out.dir

# All Build rule for target.
CMakeFiles/mobilenetTest.out.dir/all: CMakeFiles/MNN.dir/all
CMakeFiles/mobilenetTest.out.dir/all: express/CMakeFiles/MNN_Express.dir/all
	$(MAKE) -f CMakeFiles/mobilenetTest.out.dir/build.make CMakeFiles/mobilenetTest.out.dir/depend
	$(MAKE) -f CMakeFiles/mobilenetTest.out.dir/build.make CMakeFiles/mobilenetTest.out.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num= "Built target mobilenetTest.out"
.PHONY : CMakeFiles/mobilenetTest.out.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/mobilenetTest.out.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 92
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/mobilenetTest.out.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 0
.PHONY : CMakeFiles/mobilenetTest.out.dir/rule

# Convenience name for target.
mobilenetTest.out: CMakeFiles/mobilenetTest.out.dir/rule

.PHONY : mobilenetTest.out

# clean rule for target.
CMakeFiles/mobilenetTest.out.dir/clean:
	$(MAKE) -f CMakeFiles/mobilenetTest.out.dir/build.make CMakeFiles/mobilenetTest.out.dir/clean
.PHONY : CMakeFiles/mobilenetTest.out.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/MNNV2Basic.out.dir

# All Build rule for target.
CMakeFiles/MNNV2Basic.out.dir/all: CMakeFiles/MNN.dir/all
CMakeFiles/MNNV2Basic.out.dir/all: express/CMakeFiles/MNN_Express.dir/all
	$(MAKE) -f CMakeFiles/MNNV2Basic.out.dir/build.make CMakeFiles/MNNV2Basic.out.dir/depend
	$(MAKE) -f CMakeFiles/MNNV2Basic.out.dir/build.make CMakeFiles/MNNV2Basic.out.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num= "Built target MNNV2Basic.out"
.PHONY : CMakeFiles/MNNV2Basic.out.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/MNNV2Basic.out.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 92
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/MNNV2Basic.out.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 0
.PHONY : CMakeFiles/MNNV2Basic.out.dir/rule

# Convenience name for target.
MNNV2Basic.out: CMakeFiles/MNNV2Basic.out.dir/rule

.PHONY : MNNV2Basic.out

# clean rule for target.
CMakeFiles/MNNV2Basic.out.dir/clean:
	$(MAKE) -f CMakeFiles/MNNV2Basic.out.dir/build.make CMakeFiles/MNNV2Basic.out.dir/clean
.PHONY : CMakeFiles/MNNV2Basic.out.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/checkInvalidValue.out.dir

# All Build rule for target.
CMakeFiles/checkInvalidValue.out.dir/all: CMakeFiles/MNN.dir/all
CMakeFiles/checkInvalidValue.out.dir/all: express/CMakeFiles/MNN_Express.dir/all
	$(MAKE) -f CMakeFiles/checkInvalidValue.out.dir/build.make CMakeFiles/checkInvalidValue.out.dir/depend
	$(MAKE) -f CMakeFiles/checkInvalidValue.out.dir/build.make CMakeFiles/checkInvalidValue.out.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=95 "Built target checkInvalidValue.out"
.PHONY : CMakeFiles/checkInvalidValue.out.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/checkInvalidValue.out.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 93
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/checkInvalidValue.out.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 0
.PHONY : CMakeFiles/checkInvalidValue.out.dir/rule

# Convenience name for target.
checkInvalidValue.out: CMakeFiles/checkInvalidValue.out.dir/rule

.PHONY : checkInvalidValue.out

# clean rule for target.
CMakeFiles/checkInvalidValue.out.dir/clean:
	$(MAKE) -f CMakeFiles/checkInvalidValue.out.dir/build.make CMakeFiles/checkInvalidValue.out.dir/clean
.PHONY : CMakeFiles/checkInvalidValue.out.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/MNN.dir

# All Build rule for target.
CMakeFiles/MNN.dir/all: CMakeFiles/MNNUtils.dir/all
CMakeFiles/MNN.dir/all: CMakeFiles/MNN_Arm82.dir/all
CMakeFiles/MNN.dir/all: CMakeFiles/MNNARM64.dir/all
CMakeFiles/MNN.dir/all: CMakeFiles/MNNCPU.dir/all
CMakeFiles/MNN.dir/all: CMakeFiles/MNNCV.dir/all
CMakeFiles/MNN.dir/all: CMakeFiles/MNNMath.dir/all
CMakeFiles/MNN.dir/all: CMakeFiles/MNNCore.dir/all
CMakeFiles/MNN.dir/all: CMakeFiles/MNNTransform.dir/all
	$(MAKE) -f CMakeFiles/MNN.dir/build.make CMakeFiles/MNN.dir/depend
	$(MAKE) -f CMakeFiles/MNN.dir/build.make CMakeFiles/MNN.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num= "Built target MNN"
.PHONY : CMakeFiles/MNN.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/MNN.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 89
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/MNN.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 0
.PHONY : CMakeFiles/MNN.dir/rule

# Convenience name for target.
MNN: CMakeFiles/MNN.dir/rule

.PHONY : MNN

# clean rule for target.
CMakeFiles/MNN.dir/clean:
	$(MAKE) -f CMakeFiles/MNN.dir/build.make CMakeFiles/MNN.dir/clean
.PHONY : CMakeFiles/MNN.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/ModuleBasic.out.dir

# All Build rule for target.
CMakeFiles/ModuleBasic.out.dir/all: CMakeFiles/MNN.dir/all
CMakeFiles/ModuleBasic.out.dir/all: express/CMakeFiles/MNN_Express.dir/all
	$(MAKE) -f CMakeFiles/ModuleBasic.out.dir/build.make CMakeFiles/ModuleBasic.out.dir/depend
	$(MAKE) -f CMakeFiles/ModuleBasic.out.dir/build.make CMakeFiles/ModuleBasic.out.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num= "Built target ModuleBasic.out"
.PHONY : CMakeFiles/ModuleBasic.out.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/ModuleBasic.out.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 92
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/ModuleBasic.out.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 0
.PHONY : CMakeFiles/ModuleBasic.out.dir/rule

# Convenience name for target.
ModuleBasic.out: CMakeFiles/ModuleBasic.out.dir/rule

.PHONY : ModuleBasic.out

# clean rule for target.
CMakeFiles/ModuleBasic.out.dir/clean:
	$(MAKE) -f CMakeFiles/ModuleBasic.out.dir/build.make CMakeFiles/ModuleBasic.out.dir/clean
.PHONY : CMakeFiles/ModuleBasic.out.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/GetMNNInfo.dir

# All Build rule for target.
CMakeFiles/GetMNNInfo.dir/all: CMakeFiles/MNN.dir/all
CMakeFiles/GetMNNInfo.dir/all: express/CMakeFiles/MNN_Express.dir/all
	$(MAKE) -f CMakeFiles/GetMNNInfo.dir/build.make CMakeFiles/GetMNNInfo.dir/depend
	$(MAKE) -f CMakeFiles/GetMNNInfo.dir/build.make CMakeFiles/GetMNNInfo.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num= "Built target GetMNNInfo"
.PHONY : CMakeFiles/GetMNNInfo.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/GetMNNInfo.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 92
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/GetMNNInfo.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 0
.PHONY : CMakeFiles/GetMNNInfo.dir/rule

# Convenience name for target.
GetMNNInfo: CMakeFiles/GetMNNInfo.dir/rule

.PHONY : GetMNNInfo

# clean rule for target.
CMakeFiles/GetMNNInfo.dir/clean:
	$(MAKE) -f CMakeFiles/GetMNNInfo.dir/build.make CMakeFiles/GetMNNInfo.dir/clean
.PHONY : CMakeFiles/GetMNNInfo.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/MNN_Arm82.dir

# All Build rule for target.
CMakeFiles/MNN_Arm82.dir/all:
	$(MAKE) -f CMakeFiles/MNN_Arm82.dir/build.make CMakeFiles/MNN_Arm82.dir/depend
	$(MAKE) -f CMakeFiles/MNN_Arm82.dir/build.make CMakeFiles/MNN_Arm82.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=85,86,87,88,89 "Built target MNN_Arm82"
.PHONY : CMakeFiles/MNN_Arm82.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/MNN_Arm82.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 5
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/MNN_Arm82.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 0
.PHONY : CMakeFiles/MNN_Arm82.dir/rule

# Convenience name for target.
MNN_Arm82: CMakeFiles/MNN_Arm82.dir/rule

.PHONY : MNN_Arm82

# clean rule for target.
CMakeFiles/MNN_Arm82.dir/clean:
	$(MAKE) -f CMakeFiles/MNN_Arm82.dir/build.make CMakeFiles/MNN_Arm82.dir/clean
.PHONY : CMakeFiles/MNN_Arm82.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/MNNARM64.dir

# All Build rule for target.
CMakeFiles/MNNARM64.dir/all:
	$(MAKE) -f CMakeFiles/MNNARM64.dir/build.make CMakeFiles/MNNARM64.dir/depend
	$(MAKE) -f CMakeFiles/MNNARM64.dir/build.make CMakeFiles/MNNARM64.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32 "Built target MNNARM64"
.PHONY : CMakeFiles/MNNARM64.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/MNNARM64.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 32
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/MNNARM64.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 0
.PHONY : CMakeFiles/MNNARM64.dir/rule

# Convenience name for target.
MNNARM64: CMakeFiles/MNNARM64.dir/rule

.PHONY : MNNARM64

# clean rule for target.
CMakeFiles/MNNARM64.dir/clean:
	$(MAKE) -f CMakeFiles/MNNARM64.dir/build.make CMakeFiles/MNNARM64.dir/clean
.PHONY : CMakeFiles/MNNARM64.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/backendTest.out.dir

# All Build rule for target.
CMakeFiles/backendTest.out.dir/all: CMakeFiles/MNN.dir/all
CMakeFiles/backendTest.out.dir/all: express/CMakeFiles/MNN_Express.dir/all
	$(MAKE) -f CMakeFiles/backendTest.out.dir/build.make CMakeFiles/backendTest.out.dir/depend
	$(MAKE) -f CMakeFiles/backendTest.out.dir/build.make CMakeFiles/backendTest.out.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num= "Built target backendTest.out"
.PHONY : CMakeFiles/backendTest.out.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/backendTest.out.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 92
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/backendTest.out.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 0
.PHONY : CMakeFiles/backendTest.out.dir/rule

# Convenience name for target.
backendTest.out: CMakeFiles/backendTest.out.dir/rule

.PHONY : backendTest.out

# clean rule for target.
CMakeFiles/backendTest.out.dir/clean:
	$(MAKE) -f CMakeFiles/backendTest.out.dir/build.make CMakeFiles/backendTest.out.dir/clean
.PHONY : CMakeFiles/backendTest.out.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/getPerformance.out.dir

# All Build rule for target.
CMakeFiles/getPerformance.out.dir/all: CMakeFiles/MNN.dir/all
CMakeFiles/getPerformance.out.dir/all: express/CMakeFiles/MNN_Express.dir/all
	$(MAKE) -f CMakeFiles/getPerformance.out.dir/build.make CMakeFiles/getPerformance.out.dir/depend
	$(MAKE) -f CMakeFiles/getPerformance.out.dir/build.make CMakeFiles/getPerformance.out.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num= "Built target getPerformance.out"
.PHONY : CMakeFiles/getPerformance.out.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/getPerformance.out.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 92
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/getPerformance.out.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 0
.PHONY : CMakeFiles/getPerformance.out.dir/rule

# Convenience name for target.
getPerformance.out: CMakeFiles/getPerformance.out.dir/rule

.PHONY : getPerformance.out

# clean rule for target.
CMakeFiles/getPerformance.out.dir/clean:
	$(MAKE) -f CMakeFiles/getPerformance.out.dir/build.make CMakeFiles/getPerformance.out.dir/clean
.PHONY : CMakeFiles/getPerformance.out.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/checkDir.out.dir

# All Build rule for target.
CMakeFiles/checkDir.out.dir/all:
	$(MAKE) -f CMakeFiles/checkDir.out.dir/build.make CMakeFiles/checkDir.out.dir/depend
	$(MAKE) -f CMakeFiles/checkDir.out.dir/build.make CMakeFiles/checkDir.out.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=94 "Built target checkDir.out"
.PHONY : CMakeFiles/checkDir.out.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/checkDir.out.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 1
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/checkDir.out.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 0
.PHONY : CMakeFiles/checkDir.out.dir/rule

# Convenience name for target.
checkDir.out: CMakeFiles/checkDir.out.dir/rule

.PHONY : checkDir.out

# clean rule for target.
CMakeFiles/checkDir.out.dir/clean:
	$(MAKE) -f CMakeFiles/checkDir.out.dir/build.make CMakeFiles/checkDir.out.dir/clean
.PHONY : CMakeFiles/checkDir.out.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/MNNCPU.dir

# All Build rule for target.
CMakeFiles/MNNCPU.dir/all:
	$(MAKE) -f CMakeFiles/MNNCPU.dir/build.make CMakeFiles/MNNCPU.dir/depend
	$(MAKE) -f CMakeFiles/MNNCPU.dir/build.make CMakeFiles/MNNCPU.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53 "Built target MNNCPU"
.PHONY : CMakeFiles/MNNCPU.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/MNNCPU.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 21
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/MNNCPU.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 0
.PHONY : CMakeFiles/MNNCPU.dir/rule

# Convenience name for target.
MNNCPU: CMakeFiles/MNNCPU.dir/rule

.PHONY : MNNCPU

# clean rule for target.
CMakeFiles/MNNCPU.dir/clean:
	$(MAKE) -f CMakeFiles/MNNCPU.dir/build.make CMakeFiles/MNNCPU.dir/clean
.PHONY : CMakeFiles/MNNCPU.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/MNNCV.dir

# All Build rule for target.
CMakeFiles/MNNCV.dir/all:
	$(MAKE) -f CMakeFiles/MNNCV.dir/build.make CMakeFiles/MNNCV.dir/depend
	$(MAKE) -f CMakeFiles/MNNCV.dir/build.make CMakeFiles/MNNCV.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=54 "Built target MNNCV"
.PHONY : CMakeFiles/MNNCV.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/MNNCV.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 1
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/MNNCV.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 0
.PHONY : CMakeFiles/MNNCV.dir/rule

# Convenience name for target.
MNNCV: CMakeFiles/MNNCV.dir/rule

.PHONY : MNNCV

# clean rule for target.
CMakeFiles/MNNCV.dir/clean:
	$(MAKE) -f CMakeFiles/MNNCV.dir/build.make CMakeFiles/MNNCV.dir/clean
.PHONY : CMakeFiles/MNNCV.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/fuseTest.dir

# All Build rule for target.
CMakeFiles/fuseTest.dir/all: CMakeFiles/MNN.dir/all
CMakeFiles/fuseTest.dir/all: express/CMakeFiles/MNN_Express.dir/all
	$(MAKE) -f CMakeFiles/fuseTest.dir/build.make CMakeFiles/fuseTest.dir/depend
	$(MAKE) -f CMakeFiles/fuseTest.dir/build.make CMakeFiles/fuseTest.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num= "Built target fuseTest"
.PHONY : CMakeFiles/fuseTest.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/fuseTest.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 92
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/fuseTest.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 0
.PHONY : CMakeFiles/fuseTest.dir/rule

# Convenience name for target.
fuseTest: CMakeFiles/fuseTest.dir/rule

.PHONY : fuseTest

# clean rule for target.
CMakeFiles/fuseTest.dir/clean:
	$(MAKE) -f CMakeFiles/fuseTest.dir/build.make CMakeFiles/fuseTest.dir/clean
.PHONY : CMakeFiles/fuseTest.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/MNNMath.dir

# All Build rule for target.
CMakeFiles/MNNMath.dir/all:
	$(MAKE) -f CMakeFiles/MNNMath.dir/build.make CMakeFiles/MNNMath.dir/depend
	$(MAKE) -f CMakeFiles/MNNMath.dir/build.make CMakeFiles/MNNMath.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=58 "Built target MNNMath"
.PHONY : CMakeFiles/MNNMath.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/MNNMath.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 1
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/MNNMath.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 0
.PHONY : CMakeFiles/MNNMath.dir/rule

# Convenience name for target.
MNNMath: CMakeFiles/MNNMath.dir/rule

.PHONY : MNNMath

# clean rule for target.
CMakeFiles/MNNMath.dir/clean:
	$(MAKE) -f CMakeFiles/MNNMath.dir/build.make CMakeFiles/MNNMath.dir/clean
.PHONY : CMakeFiles/MNNMath.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/MNNCore.dir

# All Build rule for target.
CMakeFiles/MNNCore.dir/all:
	$(MAKE) -f CMakeFiles/MNNCore.dir/build.make CMakeFiles/MNNCore.dir/depend
	$(MAKE) -f CMakeFiles/MNNCore.dir/build.make CMakeFiles/MNNCore.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=55,56,57 "Built target MNNCore"
.PHONY : CMakeFiles/MNNCore.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/MNNCore.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 3
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/MNNCore.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 0
.PHONY : CMakeFiles/MNNCore.dir/rule

# Convenience name for target.
MNNCore: CMakeFiles/MNNCore.dir/rule

.PHONY : MNNCore

# clean rule for target.
CMakeFiles/MNNCore.dir/clean:
	$(MAKE) -f CMakeFiles/MNNCore.dir/build.make CMakeFiles/MNNCore.dir/clean
.PHONY : CMakeFiles/MNNCore.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/MNNTransform.dir

# All Build rule for target.
CMakeFiles/MNNTransform.dir/all:
	$(MAKE) -f CMakeFiles/MNNTransform.dir/build.make CMakeFiles/MNNTransform.dir/depend
	$(MAKE) -f CMakeFiles/MNNTransform.dir/build.make CMakeFiles/MNNTransform.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83 "Built target MNNTransform"
.PHONY : CMakeFiles/MNNTransform.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/MNNTransform.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 25
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/MNNTransform.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 0
.PHONY : CMakeFiles/MNNTransform.dir/rule

# Convenience name for target.
MNNTransform: CMakeFiles/MNNTransform.dir/rule

.PHONY : MNNTransform

# clean rule for target.
CMakeFiles/MNNTransform.dir/clean:
	$(MAKE) -f CMakeFiles/MNNTransform.dir/build.make CMakeFiles/MNNTransform.dir/clean
.PHONY : CMakeFiles/MNNTransform.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/SequenceModuleTest.out.dir

# All Build rule for target.
CMakeFiles/SequenceModuleTest.out.dir/all: CMakeFiles/MNN.dir/all
CMakeFiles/SequenceModuleTest.out.dir/all: express/CMakeFiles/MNN_Express.dir/all
	$(MAKE) -f CMakeFiles/SequenceModuleTest.out.dir/build.make CMakeFiles/SequenceModuleTest.out.dir/depend
	$(MAKE) -f CMakeFiles/SequenceModuleTest.out.dir/build.make CMakeFiles/SequenceModuleTest.out.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num= "Built target SequenceModuleTest.out"
.PHONY : CMakeFiles/SequenceModuleTest.out.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/SequenceModuleTest.out.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 92
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/SequenceModuleTest.out.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 0
.PHONY : CMakeFiles/SequenceModuleTest.out.dir/rule

# Convenience name for target.
SequenceModuleTest.out: CMakeFiles/SequenceModuleTest.out.dir/rule

.PHONY : SequenceModuleTest.out

# clean rule for target.
CMakeFiles/SequenceModuleTest.out.dir/clean:
	$(MAKE) -f CMakeFiles/SequenceModuleTest.out.dir/build.make CMakeFiles/SequenceModuleTest.out.dir/clean
.PHONY : CMakeFiles/SequenceModuleTest.out.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/testModel_expr.out.dir

# All Build rule for target.
CMakeFiles/testModel_expr.out.dir/all: CMakeFiles/MNN.dir/all
CMakeFiles/testModel_expr.out.dir/all: express/CMakeFiles/MNN_Express.dir/all
	$(MAKE) -f CMakeFiles/testModel_expr.out.dir/build.make CMakeFiles/testModel_expr.out.dir/depend
	$(MAKE) -f CMakeFiles/testModel_expr.out.dir/build.make CMakeFiles/testModel_expr.out.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=98 "Built target testModel_expr.out"
.PHONY : CMakeFiles/testModel_expr.out.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/testModel_expr.out.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 93
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/testModel_expr.out.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 0
.PHONY : CMakeFiles/testModel_expr.out.dir/rule

# Convenience name for target.
testModel_expr.out: CMakeFiles/testModel_expr.out.dir/rule

.PHONY : testModel_expr.out

# clean rule for target.
CMakeFiles/testModel_expr.out.dir/clean:
	$(MAKE) -f CMakeFiles/testModel_expr.out.dir/build.make CMakeFiles/testModel_expr.out.dir/clean
.PHONY : CMakeFiles/testModel_expr.out.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/modelCompare.out.dir

# All Build rule for target.
CMakeFiles/modelCompare.out.dir/all: CMakeFiles/MNN.dir/all
CMakeFiles/modelCompare.out.dir/all: express/CMakeFiles/MNN_Express.dir/all
	$(MAKE) -f CMakeFiles/modelCompare.out.dir/build.make CMakeFiles/modelCompare.out.dir/depend
	$(MAKE) -f CMakeFiles/modelCompare.out.dir/build.make CMakeFiles/modelCompare.out.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=97 "Built target modelCompare.out"
.PHONY : CMakeFiles/modelCompare.out.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/modelCompare.out.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 93
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/modelCompare.out.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 0
.PHONY : CMakeFiles/modelCompare.out.dir/rule

# Convenience name for target.
modelCompare.out: CMakeFiles/modelCompare.out.dir/rule

.PHONY : modelCompare.out

# clean rule for target.
CMakeFiles/modelCompare.out.dir/clean:
	$(MAKE) -f CMakeFiles/modelCompare.out.dir/build.make CMakeFiles/modelCompare.out.dir/clean
.PHONY : CMakeFiles/modelCompare.out.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/testModel.out.dir

# All Build rule for target.
CMakeFiles/testModel.out.dir/all: CMakeFiles/MNN.dir/all
CMakeFiles/testModel.out.dir/all: express/CMakeFiles/MNN_Express.dir/all
	$(MAKE) -f CMakeFiles/testModel.out.dir/build.make CMakeFiles/testModel.out.dir/depend
	$(MAKE) -f CMakeFiles/testModel.out.dir/build.make CMakeFiles/testModel.out.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num= "Built target testModel.out"
.PHONY : CMakeFiles/testModel.out.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/testModel.out.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 92
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/testModel.out.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 0
.PHONY : CMakeFiles/testModel.out.dir/rule

# Convenience name for target.
testModel.out: CMakeFiles/testModel.out.dir/rule

.PHONY : testModel.out

# clean rule for target.
CMakeFiles/testModel.out.dir/clean:
	$(MAKE) -f CMakeFiles/testModel.out.dir/build.make CMakeFiles/testModel.out.dir/clean
.PHONY : CMakeFiles/testModel.out.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/winogradExample.out.dir

# All Build rule for target.
CMakeFiles/winogradExample.out.dir/all: CMakeFiles/MNN.dir/all
CMakeFiles/winogradExample.out.dir/all: express/CMakeFiles/MNN_Express.dir/all
	$(MAKE) -f CMakeFiles/winogradExample.out.dir/build.make CMakeFiles/winogradExample.out.dir/depend
	$(MAKE) -f CMakeFiles/winogradExample.out.dir/build.make CMakeFiles/winogradExample.out.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=100 "Built target winogradExample.out"
.PHONY : CMakeFiles/winogradExample.out.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/winogradExample.out.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 93
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/winogradExample.out.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 0
.PHONY : CMakeFiles/winogradExample.out.dir/rule

# Convenience name for target.
winogradExample.out: CMakeFiles/winogradExample.out.dir/rule

.PHONY : winogradExample.out

# clean rule for target.
CMakeFiles/winogradExample.out.dir/clean:
	$(MAKE) -f CMakeFiles/winogradExample.out.dir/build.make CMakeFiles/winogradExample.out.dir/clean
.PHONY : CMakeFiles/winogradExample.out.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/timeProfile.out.dir

# All Build rule for target.
CMakeFiles/timeProfile.out.dir/all: CMakeFiles/MNN.dir/all
CMakeFiles/timeProfile.out.dir/all: express/CMakeFiles/MNN_Express.dir/all
	$(MAKE) -f CMakeFiles/timeProfile.out.dir/build.make CMakeFiles/timeProfile.out.dir/depend
	$(MAKE) -f CMakeFiles/timeProfile.out.dir/build.make CMakeFiles/timeProfile.out.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=99 "Built target timeProfile.out"
.PHONY : CMakeFiles/timeProfile.out.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/timeProfile.out.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 93
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/timeProfile.out.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 0
.PHONY : CMakeFiles/timeProfile.out.dir/rule

# Convenience name for target.
timeProfile.out: CMakeFiles/timeProfile.out.dir/rule

.PHONY : timeProfile.out

# clean rule for target.
CMakeFiles/timeProfile.out.dir/clean:
	$(MAKE) -f CMakeFiles/timeProfile.out.dir/build.make CMakeFiles/timeProfile.out.dir/clean
.PHONY : CMakeFiles/timeProfile.out.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/mergeInplaceForCPU.dir

# All Build rule for target.
CMakeFiles/mergeInplaceForCPU.dir/all: CMakeFiles/MNN.dir/all
CMakeFiles/mergeInplaceForCPU.dir/all: express/CMakeFiles/MNN_Express.dir/all
	$(MAKE) -f CMakeFiles/mergeInplaceForCPU.dir/build.make CMakeFiles/mergeInplaceForCPU.dir/depend
	$(MAKE) -f CMakeFiles/mergeInplaceForCPU.dir/build.make CMakeFiles/mergeInplaceForCPU.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=96 "Built target mergeInplaceForCPU"
.PHONY : CMakeFiles/mergeInplaceForCPU.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/mergeInplaceForCPU.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 93
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/mergeInplaceForCPU.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 0
.PHONY : CMakeFiles/mergeInplaceForCPU.dir/rule

# Convenience name for target.
mergeInplaceForCPU: CMakeFiles/mergeInplaceForCPU.dir/rule

.PHONY : mergeInplaceForCPU

# clean rule for target.
CMakeFiles/mergeInplaceForCPU.dir/clean:
	$(MAKE) -f CMakeFiles/mergeInplaceForCPU.dir/build.make CMakeFiles/mergeInplaceForCPU.dir/clean
.PHONY : CMakeFiles/mergeInplaceForCPU.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/testTrain.out.dir

# All Build rule for target.
CMakeFiles/testTrain.out.dir/all: CMakeFiles/MNN.dir/all
CMakeFiles/testTrain.out.dir/all: express/CMakeFiles/MNN_Express.dir/all
	$(MAKE) -f CMakeFiles/testTrain.out.dir/build.make CMakeFiles/testTrain.out.dir/depend
	$(MAKE) -f CMakeFiles/testTrain.out.dir/build.make CMakeFiles/testTrain.out.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num= "Built target testTrain.out"
.PHONY : CMakeFiles/testTrain.out.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/testTrain.out.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 92
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/testTrain.out.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 0
.PHONY : CMakeFiles/testTrain.out.dir/rule

# Convenience name for target.
testTrain.out: CMakeFiles/testTrain.out.dir/rule

.PHONY : testTrain.out

# clean rule for target.
CMakeFiles/testTrain.out.dir/clean:
	$(MAKE) -f CMakeFiles/testTrain.out.dir/build.make CMakeFiles/testTrain.out.dir/clean
.PHONY : CMakeFiles/testTrain.out.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/testModelWithDescribe.out.dir

# All Build rule for target.
CMakeFiles/testModelWithDescribe.out.dir/all: CMakeFiles/MNN.dir/all
CMakeFiles/testModelWithDescribe.out.dir/all: express/CMakeFiles/MNN_Express.dir/all
	$(MAKE) -f CMakeFiles/testModelWithDescribe.out.dir/build.make CMakeFiles/testModelWithDescribe.out.dir/depend
	$(MAKE) -f CMakeFiles/testModelWithDescribe.out.dir/build.make CMakeFiles/testModelWithDescribe.out.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num= "Built target testModelWithDescribe.out"
.PHONY : CMakeFiles/testModelWithDescribe.out.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/testModelWithDescribe.out.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 92
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/testModelWithDescribe.out.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 0
.PHONY : CMakeFiles/testModelWithDescribe.out.dir/rule

# Convenience name for target.
testModelWithDescribe.out: CMakeFiles/testModelWithDescribe.out.dir/rule

.PHONY : testModelWithDescribe.out

# clean rule for target.
CMakeFiles/testModelWithDescribe.out.dir/clean:
	$(MAKE) -f CMakeFiles/testModelWithDescribe.out.dir/build.make CMakeFiles/testModelWithDescribe.out.dir/clean
.PHONY : CMakeFiles/testModelWithDescribe.out.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/checkFile.out.dir

# All Build rule for target.
CMakeFiles/checkFile.out.dir/all:
	$(MAKE) -f CMakeFiles/checkFile.out.dir/build.make CMakeFiles/checkFile.out.dir/depend
	$(MAKE) -f CMakeFiles/checkFile.out.dir/build.make CMakeFiles/checkFile.out.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num= "Built target checkFile.out"
.PHONY : CMakeFiles/checkFile.out.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/checkFile.out.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 0
	$(MAKE) -f CMakeFiles/Makefile2 CMakeFiles/checkFile.out.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 0
.PHONY : CMakeFiles/checkFile.out.dir/rule

# Convenience name for target.
checkFile.out: CMakeFiles/checkFile.out.dir/rule

.PHONY : checkFile.out

# clean rule for target.
CMakeFiles/checkFile.out.dir/clean:
	$(MAKE) -f CMakeFiles/checkFile.out.dir/build.make CMakeFiles/checkFile.out.dir/clean
.PHONY : CMakeFiles/checkFile.out.dir/clean

#=============================================================================
# Target rules for target express/CMakeFiles/MNN_Express.dir

# All Build rule for target.
express/CMakeFiles/MNN_Express.dir/all: CMakeFiles/MNN.dir/all
	$(MAKE) -f express/CMakeFiles/MNN_Express.dir/build.make express/CMakeFiles/MNN_Express.dir/depend
	$(MAKE) -f express/CMakeFiles/MNN_Express.dir/build.make express/CMakeFiles/MNN_Express.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=90,91,92 "Built target MNN_Express"
.PHONY : express/CMakeFiles/MNN_Express.dir/all

# Build rule for subdir invocation for target.
express/CMakeFiles/MNN_Express.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 92
	$(MAKE) -f CMakeFiles/Makefile2 express/CMakeFiles/MNN_Express.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles 0
.PHONY : express/CMakeFiles/MNN_Express.dir/rule

# Convenience name for target.
MNN_Express: express/CMakeFiles/MNN_Express.dir/rule

.PHONY : MNN_Express

# clean rule for target.
express/CMakeFiles/MNN_Express.dir/clean:
	$(MAKE) -f express/CMakeFiles/MNN_Express.dir/build.make express/CMakeFiles/MNN_Express.dir/clean
.PHONY : express/CMakeFiles/MNN_Express.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

