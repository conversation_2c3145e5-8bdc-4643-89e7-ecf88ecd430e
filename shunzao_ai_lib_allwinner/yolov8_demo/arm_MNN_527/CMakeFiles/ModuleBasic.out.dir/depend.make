# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../3rd_party/rapidjson/allocators.h
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../3rd_party/rapidjson/document.h
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../3rd_party/rapidjson/encodedstream.h
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../3rd_party/rapidjson/encodings.h
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../3rd_party/rapidjson/error/error.h
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../3rd_party/rapidjson/internal/biginteger.h
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../3rd_party/rapidjson/internal/diyfp.h
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../3rd_party/rapidjson/internal/ieee754.h
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../3rd_party/rapidjson/internal/meta.h
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../3rd_party/rapidjson/internal/pow10.h
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../3rd_party/rapidjson/internal/stack.h
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../3rd_party/rapidjson/internal/strfunc.h
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../3rd_party/rapidjson/internal/strtod.h
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../3rd_party/rapidjson/internal/swap.h
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../3rd_party/rapidjson/memorystream.h
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../3rd_party/rapidjson/msinttypes/inttypes.h
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../3rd_party/rapidjson/msinttypes/stdint.h
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../3rd_party/rapidjson/rapidjson.h
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../3rd_party/rapidjson/reader.h
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../3rd_party/rapidjson/stream.h
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../include/MNN/AutoTime.hpp
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../include/MNN/ImageProcess.hpp
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../include/MNN/Interpreter.hpp
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../include/MNN/Matrix.h
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../include/MNN/Rect.h
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../include/MNN/expr/Executor.hpp
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../include/MNN/expr/ExecutorScope.hpp
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../include/MNN/expr/Expr.hpp
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../include/MNN/expr/ExprCreator.hpp
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../include/MNN/expr/MathOp.hpp
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../include/MNN/expr/Module.hpp
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../include/MNN/expr/NeuralNetWorkOp.hpp
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../source/core/MemoryFormater.h
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../tools/cpp/ExprDebug.hpp
CMakeFiles/ModuleBasic.out.dir/tools/cpp/ModuleBasic.cpp.o: ../tools/cpp/ModuleBasic.cpp

