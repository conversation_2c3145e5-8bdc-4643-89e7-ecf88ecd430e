#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

../3rd_party/OpenCLHeaders/CL/cl.h
CL/cl_version.h
-
CL/cl_platform.h
-

../3rd_party/OpenCLHeaders/CL/cl_platform.h
CL/cl_version.h
-
stdint.h
-
stddef.h
-
altivec.h
-
intrin.h
-
xmmintrin.h
-
intrin.h
-
emmintrin.h
-
mmintrin.h
-
intrin.h
-
immintrin.h
-

../3rd_party/OpenCLHeaders/CL/cl_version.h

/root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/OpenCLProgramBuildTest.cpp
fstream
-
string
-
vector
-
CL/cl.h
/root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/CL/cl.h
windows.h
-
libloaderapi.h
-
dlfcn.h
-

