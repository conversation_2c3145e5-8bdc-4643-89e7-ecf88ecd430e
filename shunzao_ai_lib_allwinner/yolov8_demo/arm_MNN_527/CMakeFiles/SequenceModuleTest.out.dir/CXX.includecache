#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

../3rd_party/flatbuffers/include/flatbuffers/base.h
assert.h
-
cstdint
-
cstddef
-
cstdlib
-
cstring
-
crtdbg.h
-
utility.h
-
utility
-
string
-
type_traits
-
vector
-
set
-
algorithm
-
iterator
-
memory
-
functional
-
stl_emulation.h
../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
string_view
-
experimental/string_view
-

../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
base.h
../3rd_party/flatbuffers/include/flatbuffers/base.h
cmath
-

../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
string
-
type_traits
-
vector
-
memory
-
limits
-
cctype
-

../3rd_party/rapidjson/allocators.h
rapidjson.h
../3rd_party/rapidjson/rapidjson.h

../3rd_party/rapidjson/document.h
reader.h
../3rd_party/rapidjson/reader.h
internal/meta.h
../3rd_party/rapidjson/internal/meta.h
internal/strfunc.h
../3rd_party/rapidjson/internal/strfunc.h
memorystream.h
../3rd_party/rapidjson/memorystream.h
encodedstream.h
../3rd_party/rapidjson/encodedstream.h
new
-
limits
-
iterator
-
utility
-

../3rd_party/rapidjson/encodedstream.h
stream.h
../3rd_party/rapidjson/stream.h
memorystream.h
../3rd_party/rapidjson/memorystream.h

../3rd_party/rapidjson/encodings.h
rapidjson.h
../3rd_party/rapidjson/rapidjson.h

../3rd_party/rapidjson/error/error.h
../rapidjson.h
../3rd_party/rapidjson/rapidjson.h

../3rd_party/rapidjson/internal/biginteger.h
../rapidjson.h
../3rd_party/rapidjson/rapidjson.h
intrin.h
-

../3rd_party/rapidjson/internal/diyfp.h
../rapidjson.h
../3rd_party/rapidjson/rapidjson.h
limits
-
intrin.h
-

../3rd_party/rapidjson/internal/ieee754.h
../rapidjson.h
../3rd_party/rapidjson/rapidjson.h

../3rd_party/rapidjson/internal/meta.h
../rapidjson.h
../3rd_party/rapidjson/rapidjson.h
type_traits
-

../3rd_party/rapidjson/internal/pow10.h
../rapidjson.h
../3rd_party/rapidjson/rapidjson.h

../3rd_party/rapidjson/internal/stack.h
../allocators.h
../3rd_party/rapidjson/allocators.h
swap.h
../3rd_party/rapidjson/internal/swap.h
cstddef
-

../3rd_party/rapidjson/internal/strfunc.h
../stream.h
../3rd_party/rapidjson/stream.h
cwchar
-

../3rd_party/rapidjson/internal/strtod.h
ieee754.h
../3rd_party/rapidjson/internal/ieee754.h
biginteger.h
../3rd_party/rapidjson/internal/biginteger.h
diyfp.h
../3rd_party/rapidjson/internal/diyfp.h
pow10.h
../3rd_party/rapidjson/internal/pow10.h
climits
-
limits
-

../3rd_party/rapidjson/internal/swap.h
../rapidjson.h
../3rd_party/rapidjson/rapidjson.h

../3rd_party/rapidjson/memorystream.h
stream.h
../3rd_party/rapidjson/stream.h

../3rd_party/rapidjson/msinttypes/inttypes.h
stdint.h
../3rd_party/rapidjson/msinttypes/stdint.h
inttypes.h
-

../3rd_party/rapidjson/msinttypes/stdint.h
stdint.h
-
limits.h
-
wchar.h
-

../3rd_party/rapidjson/rapidjson.h
cstdlib
-
cstring
-
rapidjson/...
../3rd_party/rapidjson/rapidjson/...
string
-
msinttypes/stdint.h
../3rd_party/rapidjson/msinttypes/stdint.h
msinttypes/inttypes.h
../3rd_party/rapidjson/msinttypes/inttypes.h
stdint.h
-
inttypes.h
-
endian.h
-
rapidjson/...
../3rd_party/rapidjson/rapidjson/...
cassert
-

../3rd_party/rapidjson/reader.h
allocators.h
../3rd_party/rapidjson/allocators.h
stream.h
../3rd_party/rapidjson/stream.h
encodedstream.h
../3rd_party/rapidjson/encodedstream.h
internal/meta.h
../3rd_party/rapidjson/internal/meta.h
internal/stack.h
../3rd_party/rapidjson/internal/stack.h
internal/strtod.h
../3rd_party/rapidjson/internal/strtod.h
limits
-
intrin.h
-
nmmintrin.h
-
emmintrin.h
-
arm_neon.h
-
stdexcept
-
rapidjson/error/error.h
../3rd_party/rapidjson/rapidjson/error/error.h
rapidjson/reader.h
../3rd_party/rapidjson/rapidjson/reader.h
error/error.h
../3rd_party/rapidjson/error/error.h

../3rd_party/rapidjson/stream.h
rapidjson.h
../3rd_party/rapidjson/rapidjson.h
encodings.h
../3rd_party/rapidjson/encodings.h

../include/MNN/AutoTime.hpp
stdint.h
-
stdio.h
-
MNN/MNNDefine.h
-

../include/MNN/ErrorCode.hpp

../include/MNN/HalideRuntime.h
stddef.h
-
stdint.h
-
stdbool.h
-

../include/MNN/ImageProcess.hpp
MNN/ErrorCode.hpp
-
MNN/Matrix.h
-
MNN/Tensor.hpp
-

../include/MNN/Interpreter.hpp
functional
-
map
-
memory
-
string
-
MNN/ErrorCode.hpp
-
MNN/MNNForwardType.h
-
MNN/Tensor.hpp
-

../include/MNN/MNNDefine.h
assert.h
-
stdio.h
-
TargetConditionals.h
-
hilog/log.h
-
android/log.h
-
syslog.h
-

../include/MNN/MNNForwardType.h
stdint.h
-
stddef.h
-

../include/MNN/Matrix.h
string.h
-
cstdint
-
MNN/Rect.h
-

../include/MNN/Rect.h
math.h
-
algorithm
-
utility
-
MNN/MNNDefine.h
-

../include/MNN/Tensor.hpp
vector
-
MNN/HalideRuntime.h
-
MNN/MNNDefine.h
-

../include/MNN/expr/Executor.hpp
MNN/ErrorCode.hpp
-
MNN/expr/Expr.hpp
-
MNN/Tensor.hpp
-
MNN/Interpreter.hpp
-
vector
-
mutex
-
set
-
MNN/MNNForwardType.h
-

../include/MNN/expr/ExecutorScope.hpp
MNN/expr/Executor.hpp
-

../include/MNN/expr/Expr.hpp
functional
-
string
-
vector
-
map
-
memory
-
MNN/HalideRuntime.h
-
MNN/MNNDefine.h
-

../include/MNN/expr/ExprCreator.hpp
MNN/expr/Expr.hpp
-
MNN/expr/MathOp.hpp
-
MNN/expr/NeuralNetWorkOp.hpp
-

../include/MNN/expr/MathOp.hpp

../include/MNN/expr/Module.hpp
vector
-
MNN/expr/Expr.hpp
-
MNN/expr/Executor.hpp
-
MNN/MNNForwardType.h
-

../include/MNN/expr/NeuralNetWorkOp.hpp
MNN/ImageProcess.hpp
-

../schema/current/CaffeOp_generated.h
Tensor_generated.h
../schema/current/Tensor_generated.h
Type_generated.h
../schema/current/Type_generated.h

../schema/current/ExtraInfo_generated.h
Tensor_generated.h
../schema/current/Tensor_generated.h
Type_generated.h
../schema/current/Type_generated.h

../schema/current/MNN_generated.h
CaffeOp_generated.h
../schema/current/CaffeOp_generated.h
ExtraInfo_generated.h
../schema/current/ExtraInfo_generated.h
TFQuantizeOp_generated.h
../schema/current/TFQuantizeOp_generated.h
Tensor_generated.h
../schema/current/Tensor_generated.h
TensorflowOp_generated.h
../schema/current/TensorflowOp_generated.h
Type_generated.h
../schema/current/Type_generated.h
UserDefine_generated.h
../schema/current/UserDefine_generated.h

../schema/current/TFQuantizeOp_generated.h
CaffeOp_generated.h
../schema/current/CaffeOp_generated.h
Tensor_generated.h
../schema/current/Tensor_generated.h
Type_generated.h
../schema/current/Type_generated.h

../schema/current/Tensor_generated.h
Type_generated.h
../schema/current/Type_generated.h

../schema/current/TensorflowOp_generated.h
Tensor_generated.h
../schema/current/Tensor_generated.h
Type_generated.h
../schema/current/Type_generated.h

../schema/current/Type_generated.h
flatbuffers/flatbuffers.h
../schema/current/flatbuffers/flatbuffers.h

../schema/current/UserDefine_generated.h
Tensor_generated.h
../schema/current/Tensor_generated.h
Type_generated.h
../schema/current/Type_generated.h

/root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/ExprDebug.hpp
cmath
-
fstream
-
sstream
-
MNN/AutoTime.hpp
-
MNN/expr/ExecutorScope.hpp
-

/root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/SequenceModuleTest.cpp
MNN_generated.h
/root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/MNN_generated.h
MNN/expr/Expr.hpp
-
MNN/expr/Module.hpp
-
MNN/expr/ExprCreator.hpp
-
MNN/AutoTime.hpp
-
rapidjson/document.h
/root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/rapidjson/document.h
cmath
-
ExprDebug.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/ExprDebug.hpp

