# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /root/pan/shunzao_ai_lib-develop/MNN-master

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm

# Include any dependencies generated for this target.
include CMakeFiles/SequenceModuleTest.out.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/SequenceModuleTest.out.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/SequenceModuleTest.out.dir/flags.make

CMakeFiles/SequenceModuleTest.out.dir/tools/cpp/SequenceModuleTest.cpp.o: CMakeFiles/SequenceModuleTest.out.dir/flags.make
CMakeFiles/SequenceModuleTest.out.dir/tools/cpp/SequenceModuleTest.cpp.o: ../tools/cpp/SequenceModuleTest.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/SequenceModuleTest.out.dir/tools/cpp/SequenceModuleTest.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/SequenceModuleTest.out.dir/tools/cpp/SequenceModuleTest.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/SequenceModuleTest.cpp

CMakeFiles/SequenceModuleTest.out.dir/tools/cpp/SequenceModuleTest.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/SequenceModuleTest.out.dir/tools/cpp/SequenceModuleTest.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/SequenceModuleTest.cpp > CMakeFiles/SequenceModuleTest.out.dir/tools/cpp/SequenceModuleTest.cpp.i

CMakeFiles/SequenceModuleTest.out.dir/tools/cpp/SequenceModuleTest.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/SequenceModuleTest.out.dir/tools/cpp/SequenceModuleTest.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/SequenceModuleTest.cpp -o CMakeFiles/SequenceModuleTest.out.dir/tools/cpp/SequenceModuleTest.cpp.s

# Object files for target SequenceModuleTest.out
SequenceModuleTest_out_OBJECTS = \
"CMakeFiles/SequenceModuleTest.out.dir/tools/cpp/SequenceModuleTest.cpp.o"

# External object files for target SequenceModuleTest.out
SequenceModuleTest_out_EXTERNAL_OBJECTS =

SequenceModuleTest.out: CMakeFiles/SequenceModuleTest.out.dir/tools/cpp/SequenceModuleTest.cpp.o
SequenceModuleTest.out: CMakeFiles/SequenceModuleTest.out.dir/build.make
SequenceModuleTest.out: express/libMNN_Express.so
SequenceModuleTest.out: libMNN.so
SequenceModuleTest.out: CMakeFiles/SequenceModuleTest.out.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable SequenceModuleTest.out"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/SequenceModuleTest.out.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/SequenceModuleTest.out.dir/build: SequenceModuleTest.out

.PHONY : CMakeFiles/SequenceModuleTest.out.dir/build

CMakeFiles/SequenceModuleTest.out.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/SequenceModuleTest.out.dir/cmake_clean.cmake
.PHONY : CMakeFiles/SequenceModuleTest.out.dir/clean

CMakeFiles/SequenceModuleTest.out.dir/depend:
	cd /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /root/pan/shunzao_ai_lib-develop/MNN-master /root/pan/shunzao_ai_lib-develop/MNN-master /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/SequenceModuleTest.out.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/SequenceModuleTest.out.dir/depend

