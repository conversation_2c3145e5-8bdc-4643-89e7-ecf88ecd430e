#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

../3rd_party/flatbuffers/include/flatbuffers/base.h
assert.h
-
cstdint
-
cstddef
-
cstdlib
-
cstring
-
crtdbg.h
-
utility.h
-
utility
-
string
-
type_traits
-
vector
-
set
-
algorithm
-
iterator
-
memory
-
functional
-
stl_emulation.h
../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
string_view
-
experimental/string_view
-

../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
base.h
../3rd_party/flatbuffers/include/flatbuffers/base.h
cmath
-

../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
string
-
type_traits
-
vector
-
memory
-
limits
-
cctype
-

../3rd_party/rapidjson/allocators.h
rapidjson.h
../3rd_party/rapidjson/rapidjson.h

../3rd_party/rapidjson/document.h
reader.h
../3rd_party/rapidjson/reader.h
internal/meta.h
../3rd_party/rapidjson/internal/meta.h
internal/strfunc.h
../3rd_party/rapidjson/internal/strfunc.h
memorystream.h
../3rd_party/rapidjson/memorystream.h
encodedstream.h
../3rd_party/rapidjson/encodedstream.h
new
-
limits
-
iterator
-
utility
-

../3rd_party/rapidjson/encodedstream.h
stream.h
../3rd_party/rapidjson/stream.h
memorystream.h
../3rd_party/rapidjson/memorystream.h

../3rd_party/rapidjson/encodings.h
rapidjson.h
../3rd_party/rapidjson/rapidjson.h

../3rd_party/rapidjson/error/error.h
../rapidjson.h
../3rd_party/rapidjson/rapidjson.h

../3rd_party/rapidjson/internal/biginteger.h
../rapidjson.h
../3rd_party/rapidjson/rapidjson.h
intrin.h
-

../3rd_party/rapidjson/internal/diyfp.h
../rapidjson.h
../3rd_party/rapidjson/rapidjson.h
limits
-
intrin.h
-

../3rd_party/rapidjson/internal/ieee754.h
../rapidjson.h
../3rd_party/rapidjson/rapidjson.h

../3rd_party/rapidjson/internal/meta.h
../rapidjson.h
../3rd_party/rapidjson/rapidjson.h
type_traits
-

../3rd_party/rapidjson/internal/pow10.h
../rapidjson.h
../3rd_party/rapidjson/rapidjson.h

../3rd_party/rapidjson/internal/stack.h
../allocators.h
../3rd_party/rapidjson/allocators.h
swap.h
../3rd_party/rapidjson/internal/swap.h
cstddef
-

../3rd_party/rapidjson/internal/strfunc.h
../stream.h
../3rd_party/rapidjson/stream.h
cwchar
-

../3rd_party/rapidjson/internal/strtod.h
ieee754.h
../3rd_party/rapidjson/internal/ieee754.h
biginteger.h
../3rd_party/rapidjson/internal/biginteger.h
diyfp.h
../3rd_party/rapidjson/internal/diyfp.h
pow10.h
../3rd_party/rapidjson/internal/pow10.h
climits
-
limits
-

../3rd_party/rapidjson/internal/swap.h
../rapidjson.h
../3rd_party/rapidjson/rapidjson.h

../3rd_party/rapidjson/memorystream.h
stream.h
../3rd_party/rapidjson/stream.h

../3rd_party/rapidjson/msinttypes/inttypes.h
stdint.h
../3rd_party/rapidjson/msinttypes/stdint.h
inttypes.h
-

../3rd_party/rapidjson/msinttypes/stdint.h
stdint.h
-
limits.h
-
wchar.h
-

../3rd_party/rapidjson/rapidjson.h
cstdlib
-
cstring
-
rapidjson/...
../3rd_party/rapidjson/rapidjson/...
string
-
msinttypes/stdint.h
../3rd_party/rapidjson/msinttypes/stdint.h
msinttypes/inttypes.h
../3rd_party/rapidjson/msinttypes/inttypes.h
stdint.h
-
inttypes.h
-
endian.h
-
rapidjson/...
../3rd_party/rapidjson/rapidjson/...
cassert
-

../3rd_party/rapidjson/reader.h
allocators.h
../3rd_party/rapidjson/allocators.h
stream.h
../3rd_party/rapidjson/stream.h
encodedstream.h
../3rd_party/rapidjson/encodedstream.h
internal/meta.h
../3rd_party/rapidjson/internal/meta.h
internal/stack.h
../3rd_party/rapidjson/internal/stack.h
internal/strtod.h
../3rd_party/rapidjson/internal/strtod.h
limits
-
intrin.h
-
nmmintrin.h
-
emmintrin.h
-
arm_neon.h
-
stdexcept
-
rapidjson/error/error.h
../3rd_party/rapidjson/rapidjson/error/error.h
rapidjson/reader.h
../3rd_party/rapidjson/rapidjson/reader.h
error/error.h
../3rd_party/rapidjson/error/error.h

../3rd_party/rapidjson/stream.h
rapidjson.h
../3rd_party/rapidjson/rapidjson.h
encodings.h
../3rd_party/rapidjson/encodings.h

../include/MNN/AutoTime.hpp
stdint.h
-
stdio.h
-
MNN/MNNDefine.h
-

../include/MNN/ErrorCode.hpp

../include/MNN/HalideRuntime.h
stddef.h
-
stdint.h
-
stdbool.h
-

../include/MNN/Interpreter.hpp
functional
-
map
-
memory
-
string
-
MNN/ErrorCode.hpp
-
MNN/MNNForwardType.h
-
MNN/Tensor.hpp
-

../include/MNN/MNNDefine.h
assert.h
-
stdio.h
-
TargetConditionals.h
-
hilog/log.h
-
android/log.h
-
syslog.h
-

../include/MNN/MNNForwardType.h
stdint.h
-
stddef.h
-

../include/MNN/Tensor.hpp
vector
-
MNN/HalideRuntime.h
-
MNN/MNNDefine.h
-

../include/MNN/expr/Executor.hpp
MNN/ErrorCode.hpp
-
MNN/expr/Expr.hpp
-
MNN/Tensor.hpp
-
MNN/Interpreter.hpp
-
vector
-
mutex
-
set
-
MNN/MNNForwardType.h
-

../include/MNN/expr/Expr.hpp
functional
-
string
-
vector
-
map
-
memory
-
MNN/HalideRuntime.h
-
MNN/MNNDefine.h
-

../include/MNN/expr/Module.hpp
vector
-
MNN/expr/Expr.hpp
-
MNN/expr/Executor.hpp
-
MNN/MNNForwardType.h
-

../schema/current/CaffeOp_generated.h
Tensor_generated.h
../schema/current/Tensor_generated.h
Type_generated.h
../schema/current/Type_generated.h

../schema/current/ExtraInfo_generated.h
Tensor_generated.h
../schema/current/Tensor_generated.h
Type_generated.h
../schema/current/Type_generated.h

../schema/current/MNN_generated.h
CaffeOp_generated.h
../schema/current/CaffeOp_generated.h
ExtraInfo_generated.h
../schema/current/ExtraInfo_generated.h
TFQuantizeOp_generated.h
../schema/current/TFQuantizeOp_generated.h
Tensor_generated.h
../schema/current/Tensor_generated.h
TensorflowOp_generated.h
../schema/current/TensorflowOp_generated.h
Type_generated.h
../schema/current/Type_generated.h
UserDefine_generated.h
../schema/current/UserDefine_generated.h

../schema/current/TFQuantizeOp_generated.h
CaffeOp_generated.h
../schema/current/CaffeOp_generated.h
Tensor_generated.h
../schema/current/Tensor_generated.h
Type_generated.h
../schema/current/Type_generated.h

../schema/current/Tensor_generated.h
Type_generated.h
../schema/current/Type_generated.h

../schema/current/TensorflowOp_generated.h
Tensor_generated.h
../schema/current/Tensor_generated.h
Type_generated.h
../schema/current/Type_generated.h

../schema/current/Type_generated.h
flatbuffers/flatbuffers.h
../schema/current/flatbuffers/flatbuffers.h

../schema/current/UserDefine_generated.h
Tensor_generated.h
../schema/current/Tensor_generated.h
Type_generated.h
../schema/current/Type_generated.h

../source/core/AutoStorage.h
stdint.h
-
string.h
-
MNNMemoryUtils.h
../source/core/MNNMemoryUtils.h

../source/core/Backend.hpp
MNN/MNNForwardType.h
-
MNN/ErrorCode.hpp
-
map
-
Command.hpp
../source/core/Command.hpp
NonCopyable.hpp
../source/core/NonCopyable.hpp
BufferAllocator.hpp
../source/core/BufferAllocator.hpp
future
-
atomic
-

../source/core/BufferAllocator.hpp
map
-
set
-
memory
-
vector
-
MNNMemoryUtils.h
../source/core/MNNMemoryUtils.h
NonCopyable.hpp
../source/core/NonCopyable.hpp
AutoStorage.h
../source/core/AutoStorage.h
MNN/Tensor.hpp
-
MNN/ErrorCode.hpp
-

../source/core/Command.hpp
MNN/Tensor.hpp
-
AutoStorage.h
../source/core/AutoStorage.h
string
-
memory
-

../source/core/Execution.hpp
MNN/MNNForwardType.h
-
MNN/ErrorCode.hpp
-
MNN/Tensor.hpp
-
memory
-
string
-
NonCopyable.hpp
../source/core/NonCopyable.hpp

../source/core/MNNMemoryUtils.h
stdio.h
-
core/Macro.h
../source/core/core/Macro.h

../source/core/Macro.h
MNN/MNNDefine.h
-
BaseTsd.h
-

../source/core/NonCopyable.hpp

../source/core/Pipeline.hpp
Schedule.hpp
../source/core/Schedule.hpp
core/Execution.hpp
../source/core/core/Execution.hpp
geometry/GeometryComputer.hpp
../source/core/geometry/GeometryComputer.hpp

../source/core/Schedule.hpp
stdio.h
-
MNN/Interpreter.hpp
-
map
-
string
-
vector
-
array
-
core/Backend.hpp
../source/core/core/Backend.hpp
core/Command.hpp
../source/core/core/Command.hpp

../source/core/Session.hpp
MNN/Tensor.hpp
-
map
-
memory
-
vector
-
Pipeline.hpp
../source/core/Pipeline.hpp
Schedule.hpp
../source/core/Schedule.hpp
core/Backend.hpp
../source/core/core/Backend.hpp
core/Macro.h
../source/core/core/Macro.h
shape/SizeComputer.hpp
../source/core/shape/SizeComputer.hpp

../source/core/TensorUtils.hpp
MNN/Tensor.hpp
-
Backend.hpp
../source/core/Backend.hpp
AutoStorage.h
../source/core/AutoStorage.h
Tensor_generated.h
../source/core/Tensor_generated.h

../source/geometry/GeometryComputer.hpp
map
-
vector
-
MNN_generated.h
../source/geometry/MNN_generated.h
core/Command.hpp
../source/geometry/core/Command.hpp
core/TensorUtils.hpp
../source/geometry/core/TensorUtils.hpp
core/Backend.hpp
../source/geometry/core/Backend.hpp

../source/shape/SizeComputer.hpp
MNN/Tensor.hpp
-
map
-
string
-
vector
-
MNN_generated.h
../source/shape/MNN_generated.h
core/Execution.hpp
../source/shape/core/Execution.hpp
core/TensorUtils.hpp
../source/shape/core/TensorUtils.hpp

/root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/backendTest.cpp
math.h
-
stdlib.h
-
cstdlib
-
algorithm
-
cstring
-
fstream
-
iostream
-
map
-
sstream
-
string
-
MNN_generated.h
/root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/MNN_generated.h
MNN/expr/Module.hpp
-
MNN/AutoTime.hpp
-
MNN/Interpreter.hpp
-
MNN/Tensor.hpp
-
core/TensorUtils.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/core/TensorUtils.hpp
core/Session.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/core/Session.hpp
rapidjson/document.h
/root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/rapidjson/document.h

