# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# compile CXX with /root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++
CXX_FLAGS =  -std=c++11 -D__STRICT_ANSI__ -O3 -fvisibility-inlines-hidden -fvisibility=hidden -fomit-frame-pointer -funwind-tables -fstrict-aliasing -ffunction-sections -fdata-sections -fno-rtti -fno-exceptions  -O3 -DNDEBUG   -fPIC -std=gnu++11

CXX_DEFINES = -DMNN_KLEIDIAI_ENABLED=1 -DMNN_SME2 -DMNN_SUPPORT_DEPRECATED_OPV2 -DMNN_SUPPORT_QUANT_EXTEND -DMNN_USE_NEON -DMNN_USE_THREAD_POOL

CXX_INCLUDES = -I/root/pan/shunzao_ai_lib-develop/MNN-master/include -I/root/pan/shunzao_ai_lib-develop/MNN-master/source -I/root/pan/shunzao_ai_lib-develop/MNN-master/express -I/root/pan/shunzao_ai_lib-develop/MNN-master/tools -I/root/pan/shunzao_ai_lib-develop/MNN-master/codegen -I/root/pan/shunzao_ai_lib-develop/MNN-master/schema/current -I/root/pan/shunzao_ai_lib-develop/MNN-master/3rd_party -I/root/pan/shunzao_ai_lib-develop/MNN-master/3rd_party/flatbuffers/include -I/root/pan/shunzao_ai_lib-develop/MNN-master/3rd_party/half -I/root/pan/shunzao_ai_lib-develop/MNN-master/3rd_party/imageHelper -I/root/pan/shunzao_ai_lib-develop/MNN-master/3rd_party/OpenCLHeaders -I/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/_deps/kleidiai-v1.9.0 -I/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/_deps/kleidiai-v1.9.0/kai -I/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/_deps/kleidiai-v1.9.0/kai/ukernels -I/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/_deps/kleidiai-v1.9.0/kai/ukernels/matmul -I/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/_deps/kleidiai-v1.9.0/kai/ukernels/matmul/matmul_clamp_f32_qai8dxp_qsi4cxp -I/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/_deps/kleidiai-v1.9.0/kai/ukernels/matmul/matmul_clamp_f16_qsi8d32p_qai4c32p -I/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/_deps/kleidiai-v1.9.0/kai/ukernels/matmul/matmul_clamp_f32_qsi8d32p_qai4c32p -I/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/_deps/kleidiai-v1.9.0/kai/ukernels/matmul/pack -I/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/_deps/kleidiai-v1.9.0/kai/ukernels/matmul/matmul_clamp_f32_f32p_f32p -I/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/_deps/kleidiai-v1.9.0/kai/ukernels/matmul/matmul_clamp_f32_f32_f32p -I/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/_deps/kleidiai-v1.9.0/kai/ukernels/matmul/matmul_clamp_f16_f16p_f16p -I/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/_deps/kleidiai-v1.9.0/kai/ukernels/matmul/matmul_clamp_f16_f16_f16p -I/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/_deps/kleidiai-v1.9.0/kai/ukernels/matmul/imatmul_clamp_f32_f32p_f32p -I/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/_deps/kleidiai-v1.9.0/kai/ukernels/matmul/imatmul_clamp_f16_f16p_f16p 

