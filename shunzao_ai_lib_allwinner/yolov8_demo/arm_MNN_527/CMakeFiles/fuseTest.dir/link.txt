/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++   -std=c++11 -D__STRICT_ANSI__ -O3 -fvisibility-inlines-hidden -fvisibility=hidden -fomit-frame-pointer -funwind-tables -fstrict-aliasing -ffunction-sections -fdata-sections -fno-rtti -fno-exceptions  -O3 -DNDEBUG   CMakeFiles/fuseTest.dir/tools/cpp/fuseTest.cpp.o  -o fuseTest  -Wl,-rpath,/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/express:/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm express/libMNN_Express.so libMNN.so -lpthread -pthread -ldl 
