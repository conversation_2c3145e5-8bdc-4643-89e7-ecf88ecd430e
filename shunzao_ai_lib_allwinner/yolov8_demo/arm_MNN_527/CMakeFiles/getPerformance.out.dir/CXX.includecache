#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

../include/MNN/AutoTime.hpp
stdint.h
-
stdio.h
-
MNN/MNNDefine.h
-

../include/MNN/MNNDefine.h
assert.h
-
stdio.h
-
TargetConditionals.h
-
hilog/log.h
-
android/log.h
-
syslog.h
-

../source/core/Macro.h
MNN/MNNDefine.h
-
BaseTsd.h
-

/root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/getPerformance.cpp
string.h
-
chrono
-
cstdint
-
vector
-
thread
-
MNN/AutoTime.hpp
-
stdlib.h
-
MNN/MNNDefine.h
-
core/Macro.h
/root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/core/Macro.h
arm_neon.h
-

