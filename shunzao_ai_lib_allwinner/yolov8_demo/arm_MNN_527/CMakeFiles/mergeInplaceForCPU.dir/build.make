# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /root/pan/shunzao_ai_lib-develop/MNN-master

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm

# Include any dependencies generated for this target.
include CMakeFiles/mergeInplaceForCPU.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/mergeInplaceForCPU.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/mergeInplaceForCPU.dir/flags.make

CMakeFiles/mergeInplaceForCPU.dir/tools/cpp/mergeInplaceForCPU.cpp.o: CMakeFiles/mergeInplaceForCPU.dir/flags.make
CMakeFiles/mergeInplaceForCPU.dir/tools/cpp/mergeInplaceForCPU.cpp.o: ../tools/cpp/mergeInplaceForCPU.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/mergeInplaceForCPU.dir/tools/cpp/mergeInplaceForCPU.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/mergeInplaceForCPU.dir/tools/cpp/mergeInplaceForCPU.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/mergeInplaceForCPU.cpp

CMakeFiles/mergeInplaceForCPU.dir/tools/cpp/mergeInplaceForCPU.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/mergeInplaceForCPU.dir/tools/cpp/mergeInplaceForCPU.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/mergeInplaceForCPU.cpp > CMakeFiles/mergeInplaceForCPU.dir/tools/cpp/mergeInplaceForCPU.cpp.i

CMakeFiles/mergeInplaceForCPU.dir/tools/cpp/mergeInplaceForCPU.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/mergeInplaceForCPU.dir/tools/cpp/mergeInplaceForCPU.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/mergeInplaceForCPU.cpp -o CMakeFiles/mergeInplaceForCPU.dir/tools/cpp/mergeInplaceForCPU.cpp.s

# Object files for target mergeInplaceForCPU
mergeInplaceForCPU_OBJECTS = \
"CMakeFiles/mergeInplaceForCPU.dir/tools/cpp/mergeInplaceForCPU.cpp.o"

# External object files for target mergeInplaceForCPU
mergeInplaceForCPU_EXTERNAL_OBJECTS =

mergeInplaceForCPU: CMakeFiles/mergeInplaceForCPU.dir/tools/cpp/mergeInplaceForCPU.cpp.o
mergeInplaceForCPU: CMakeFiles/mergeInplaceForCPU.dir/build.make
mergeInplaceForCPU: express/libMNN_Express.so
mergeInplaceForCPU: libMNN.so
mergeInplaceForCPU: CMakeFiles/mergeInplaceForCPU.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable mergeInplaceForCPU"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/mergeInplaceForCPU.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/mergeInplaceForCPU.dir/build: mergeInplaceForCPU

.PHONY : CMakeFiles/mergeInplaceForCPU.dir/build

CMakeFiles/mergeInplaceForCPU.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/mergeInplaceForCPU.dir/cmake_clean.cmake
.PHONY : CMakeFiles/mergeInplaceForCPU.dir/clean

CMakeFiles/mergeInplaceForCPU.dir/depend:
	cd /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /root/pan/shunzao_ai_lib-develop/MNN-master /root/pan/shunzao_ai_lib-develop/MNN-master /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/mergeInplaceForCPU.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/mergeInplaceForCPU.dir/depend

