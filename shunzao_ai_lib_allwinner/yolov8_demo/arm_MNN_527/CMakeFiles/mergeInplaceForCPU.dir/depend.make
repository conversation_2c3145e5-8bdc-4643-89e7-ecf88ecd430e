# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

CMakeFiles/mergeInplaceForCPU.dir/tools/cpp/mergeInplaceForCPU.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/mergeInplaceForCPU.dir/tools/cpp/mergeInplaceForCPU.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/mergeInplaceForCPU.dir/tools/cpp/mergeInplaceForCPU.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/mergeInplaceForCPU.dir/tools/cpp/mergeInplaceForCPU.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/mergeInplaceForCPU.dir/tools/cpp/mergeInplaceForCPU.cpp.o: ../schema/current/CaffeOp_generated.h
CMakeFiles/mergeInplaceForCPU.dir/tools/cpp/mergeInplaceForCPU.cpp.o: ../schema/current/ExtraInfo_generated.h
CMakeFiles/mergeInplaceForCPU.dir/tools/cpp/mergeInplaceForCPU.cpp.o: ../schema/current/MNN_generated.h
CMakeFiles/mergeInplaceForCPU.dir/tools/cpp/mergeInplaceForCPU.cpp.o: ../schema/current/TFQuantizeOp_generated.h
CMakeFiles/mergeInplaceForCPU.dir/tools/cpp/mergeInplaceForCPU.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/mergeInplaceForCPU.dir/tools/cpp/mergeInplaceForCPU.cpp.o: ../schema/current/TensorflowOp_generated.h
CMakeFiles/mergeInplaceForCPU.dir/tools/cpp/mergeInplaceForCPU.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/mergeInplaceForCPU.dir/tools/cpp/mergeInplaceForCPU.cpp.o: ../schema/current/UserDefine_generated.h
CMakeFiles/mergeInplaceForCPU.dir/tools/cpp/mergeInplaceForCPU.cpp.o: ../tools/cpp/mergeInplaceForCPU.cpp

