#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

../3rd_party/imageHelper/stb_image.h
stb_image.h
../3rd_party/imageHelper/stb_image.h
stdio.h
-
stdlib.h
-
stdarg.h
-
stddef.h
-
stdlib.h
-
string.h
-
limits.h
-
math.h
-
stdio.h
-
assert.h
-
stdint.h
-
emmintrin.h
-
intrin.h
-
arm_neon.h
-

../3rd_party/imageHelper/stb_image_write.h
stdlib.h
-
stdio.h
-
stdarg.h
-
stdlib.h
-
string.h
-
math.h
-
assert.h
-

../include/MNN/AutoTime.hpp
stdint.h
-
stdio.h
-
MNN/MNNDefine.h
-

../include/MNN/ErrorCode.hpp

../include/MNN/HalideRuntime.h
stddef.h
-
stdint.h
-
stdbool.h
-

../include/MNN/ImageProcess.hpp
MNN/ErrorCode.hpp
-
MNN/Matrix.h
-
MNN/Tensor.hpp
-

../include/MNN/Interpreter.hpp
functional
-
map
-
memory
-
string
-
MNN/ErrorCode.hpp
-
MNN/MNNForwardType.h
-
MNN/Tensor.hpp
-

../include/MNN/MNNDefine.h
assert.h
-
stdio.h
-
TargetConditionals.h
-
hilog/log.h
-
android/log.h
-
syslog.h
-

../include/MNN/MNNForwardType.h
stdint.h
-
stddef.h
-

../include/MNN/Matrix.h
string.h
-
cstdint
-
MNN/Rect.h
-

../include/MNN/Rect.h
math.h
-
algorithm
-
utility
-
MNN/MNNDefine.h
-

../include/MNN/Tensor.hpp
vector
-
MNN/HalideRuntime.h
-
MNN/MNNDefine.h
-

/root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/mobilenetTest.cpp
stdio.h
-
MNN/ImageProcess.hpp
-
MNN/Interpreter.hpp
-
algorithm
-
fstream
-
functional
-
memory
-
sstream
-
vector
-
MNN/AutoTime.hpp
-
stb_image.h
/root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/stb_image.h
stb_image_write.h
/root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/stb_image_write.h

