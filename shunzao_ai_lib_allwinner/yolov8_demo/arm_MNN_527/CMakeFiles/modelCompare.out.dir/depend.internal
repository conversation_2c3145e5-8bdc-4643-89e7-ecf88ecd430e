# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

CMakeFiles/modelCompare.out.dir/tools/cpp/modelCompare.cpp.o
 ../3rd_party/flatbuffers/include/flatbuffers/base.h
 ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
 ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
 ../3rd_party/rapidjson/allocators.h
 ../3rd_party/rapidjson/document.h
 ../3rd_party/rapidjson/encodedstream.h
 ../3rd_party/rapidjson/encodings.h
 ../3rd_party/rapidjson/error/error.h
 ../3rd_party/rapidjson/internal/biginteger.h
 ../3rd_party/rapidjson/internal/diyfp.h
 ../3rd_party/rapidjson/internal/ieee754.h
 ../3rd_party/rapidjson/internal/meta.h
 ../3rd_party/rapidjson/internal/pow10.h
 ../3rd_party/rapidjson/internal/stack.h
 ../3rd_party/rapidjson/internal/strfunc.h
 ../3rd_party/rapidjson/internal/strtod.h
 ../3rd_party/rapidjson/internal/swap.h
 ../3rd_party/rapidjson/memorystream.h
 ../3rd_party/rapidjson/msinttypes/inttypes.h
 ../3rd_party/rapidjson/msinttypes/stdint.h
 ../3rd_party/rapidjson/rapidjson.h
 ../3rd_party/rapidjson/reader.h
 ../3rd_party/rapidjson/stream.h
 ../include/MNN/AutoTime.hpp
 ../include/MNN/ErrorCode.hpp
 ../include/MNN/HalideRuntime.h
 ../include/MNN/Interpreter.hpp
 ../include/MNN/MNNDefine.h
 ../include/MNN/MNNForwardType.h
 ../include/MNN/Tensor.hpp
 ../schema/current/Tensor_generated.h
 ../schema/current/Type_generated.h
 ../source/core/AutoStorage.h
 ../source/core/Backend.hpp
 ../source/core/BufferAllocator.hpp
 ../source/core/Command.hpp
 ../source/core/MNNMemoryUtils.h
 ../source/core/Macro.h
 ../source/core/NonCopyable.hpp
 ../source/core/TensorUtils.hpp
 /root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/modelCompare.cpp
