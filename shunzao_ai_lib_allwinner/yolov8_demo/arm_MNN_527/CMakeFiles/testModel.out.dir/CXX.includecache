#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

../3rd_party/flatbuffers/include/flatbuffers/base.h
assert.h
-
cstdint
-
cstddef
-
cstdlib
-
cstring
-
crtdbg.h
-
utility.h
-
utility
-
string
-
type_traits
-
vector
-
set
-
algorithm
-
iterator
-
memory
-
functional
-
stl_emulation.h
../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
string_view
-
experimental/string_view
-

../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
base.h
../3rd_party/flatbuffers/include/flatbuffers/base.h
cmath
-

../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
string
-
type_traits
-
vector
-
memory
-
limits
-
cctype
-

../include/MNN/AutoTime.hpp
stdint.h
-
stdio.h
-
MNN/MNNDefine.h
-

../include/MNN/ErrorCode.hpp

../include/MNN/HalideRuntime.h
stddef.h
-
stdint.h
-
stdbool.h
-

../include/MNN/Interpreter.hpp
functional
-
map
-
memory
-
string
-
MNN/ErrorCode.hpp
-
MNN/MNNForwardType.h
-
MNN/Tensor.hpp
-

../include/MNN/MNNDefine.h
assert.h
-
stdio.h
-
TargetConditionals.h
-
hilog/log.h
-
android/log.h
-
syslog.h
-

../include/MNN/MNNForwardType.h
stdint.h
-
stddef.h
-

../include/MNN/MNNSharedContext.h
MNNDefine.h
../include/MNN/MNNDefine.h
stdint.h
-

../include/MNN/Tensor.hpp
vector
-
MNN/HalideRuntime.h
-
MNN/MNNDefine.h
-

../schema/current/Tensor_generated.h
Type_generated.h
../schema/current/Type_generated.h

../schema/current/Type_generated.h
flatbuffers/flatbuffers.h
../schema/current/flatbuffers/flatbuffers.h

../source/core/AutoStorage.h
stdint.h
-
string.h
-
MNNMemoryUtils.h
../source/core/MNNMemoryUtils.h

../source/core/Backend.hpp
MNN/MNNForwardType.h
-
MNN/ErrorCode.hpp
-
map
-
Command.hpp
../source/core/Command.hpp
NonCopyable.hpp
../source/core/NonCopyable.hpp
BufferAllocator.hpp
../source/core/BufferAllocator.hpp
future
-
atomic
-

../source/core/BufferAllocator.hpp
map
-
set
-
memory
-
vector
-
MNNMemoryUtils.h
../source/core/MNNMemoryUtils.h
NonCopyable.hpp
../source/core/NonCopyable.hpp
AutoStorage.h
../source/core/AutoStorage.h
MNN/Tensor.hpp
-
MNN/ErrorCode.hpp
-

../source/core/Command.hpp
MNN/Tensor.hpp
-
AutoStorage.h
../source/core/AutoStorage.h
string
-
memory
-

../source/core/MNNMemoryUtils.h
stdio.h
-
core/Macro.h
../source/core/core/Macro.h

../source/core/Macro.h
MNN/MNNDefine.h
-
BaseTsd.h
-

../source/core/NonCopyable.hpp

../source/core/TensorUtils.hpp
MNN/Tensor.hpp
-
Backend.hpp
../source/core/Backend.hpp
AutoStorage.h
../source/core/AutoStorage.h
Tensor_generated.h
../source/core/Tensor_generated.h

/root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/testModel.cpp
MNN/MNNDefine.h
-
math.h
-
stdio.h
-
stdlib.h
-
string.h
-
MNN/AutoTime.hpp
-
MNN/Interpreter.hpp
-
MNN/Tensor.hpp
-
fstream
-
map
-
sstream
-
core/Backend.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/core/Backend.hpp
core/Macro.h
/root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/core/Macro.h
core/TensorUtils.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/core/TensorUtils.hpp
MNN/MNNSharedContext.h
/root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/MNN/MNNSharedContext.h

