# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

CMakeFiles/testModel.out.dir/tools/cpp/testModel.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/base.h
CMakeFiles/testModel.out.dir/tools/cpp/testModel.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/flatbuffers.h
CMakeFiles/testModel.out.dir/tools/cpp/testModel.cpp.o: ../3rd_party/flatbuffers/include/flatbuffers/stl_emulation.h
CMakeFiles/testModel.out.dir/tools/cpp/testModel.cpp.o: ../include/MNN/AutoTime.hpp
CMakeFiles/testModel.out.dir/tools/cpp/testModel.cpp.o: ../include/MNN/ErrorCode.hpp
CMakeFiles/testModel.out.dir/tools/cpp/testModel.cpp.o: ../include/MNN/HalideRuntime.h
CMakeFiles/testModel.out.dir/tools/cpp/testModel.cpp.o: ../include/MNN/Interpreter.hpp
CMakeFiles/testModel.out.dir/tools/cpp/testModel.cpp.o: ../include/MNN/MNNDefine.h
CMakeFiles/testModel.out.dir/tools/cpp/testModel.cpp.o: ../include/MNN/MNNForwardType.h
CMakeFiles/testModel.out.dir/tools/cpp/testModel.cpp.o: ../include/MNN/MNNSharedContext.h
CMakeFiles/testModel.out.dir/tools/cpp/testModel.cpp.o: ../include/MNN/Tensor.hpp
CMakeFiles/testModel.out.dir/tools/cpp/testModel.cpp.o: ../schema/current/Tensor_generated.h
CMakeFiles/testModel.out.dir/tools/cpp/testModel.cpp.o: ../schema/current/Type_generated.h
CMakeFiles/testModel.out.dir/tools/cpp/testModel.cpp.o: ../source/core/AutoStorage.h
CMakeFiles/testModel.out.dir/tools/cpp/testModel.cpp.o: ../source/core/Backend.hpp
CMakeFiles/testModel.out.dir/tools/cpp/testModel.cpp.o: ../source/core/BufferAllocator.hpp
CMakeFiles/testModel.out.dir/tools/cpp/testModel.cpp.o: ../source/core/Command.hpp
CMakeFiles/testModel.out.dir/tools/cpp/testModel.cpp.o: ../source/core/MNNMemoryUtils.h
CMakeFiles/testModel.out.dir/tools/cpp/testModel.cpp.o: ../source/core/Macro.h
CMakeFiles/testModel.out.dir/tools/cpp/testModel.cpp.o: ../source/core/NonCopyable.hpp
CMakeFiles/testModel.out.dir/tools/cpp/testModel.cpp.o: ../source/core/TensorUtils.hpp
CMakeFiles/testModel.out.dir/tools/cpp/testModel.cpp.o: ../tools/cpp/testModel.cpp

