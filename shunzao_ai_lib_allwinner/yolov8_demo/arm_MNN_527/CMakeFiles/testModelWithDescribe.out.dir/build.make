# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /root/pan/shunzao_ai_lib-develop/MNN-master

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm

# Include any dependencies generated for this target.
include CMakeFiles/testModelWithDescribe.out.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/testModelWithDescribe.out.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/testModelWithDescribe.out.dir/flags.make

CMakeFiles/testModelWithDescribe.out.dir/tools/cpp/testModelWithDescribe.cpp.o: CMakeFiles/testModelWithDescribe.out.dir/flags.make
CMakeFiles/testModelWithDescribe.out.dir/tools/cpp/testModelWithDescribe.cpp.o: ../tools/cpp/testModelWithDescribe.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/testModelWithDescribe.out.dir/tools/cpp/testModelWithDescribe.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/testModelWithDescribe.out.dir/tools/cpp/testModelWithDescribe.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/testModelWithDescribe.cpp

CMakeFiles/testModelWithDescribe.out.dir/tools/cpp/testModelWithDescribe.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/testModelWithDescribe.out.dir/tools/cpp/testModelWithDescribe.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/testModelWithDescribe.cpp > CMakeFiles/testModelWithDescribe.out.dir/tools/cpp/testModelWithDescribe.cpp.i

CMakeFiles/testModelWithDescribe.out.dir/tools/cpp/testModelWithDescribe.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/testModelWithDescribe.out.dir/tools/cpp/testModelWithDescribe.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/testModelWithDescribe.cpp -o CMakeFiles/testModelWithDescribe.out.dir/tools/cpp/testModelWithDescribe.cpp.s

# Object files for target testModelWithDescribe.out
testModelWithDescribe_out_OBJECTS = \
"CMakeFiles/testModelWithDescribe.out.dir/tools/cpp/testModelWithDescribe.cpp.o"

# External object files for target testModelWithDescribe.out
testModelWithDescribe_out_EXTERNAL_OBJECTS =

testModelWithDescribe.out: CMakeFiles/testModelWithDescribe.out.dir/tools/cpp/testModelWithDescribe.cpp.o
testModelWithDescribe.out: CMakeFiles/testModelWithDescribe.out.dir/build.make
testModelWithDescribe.out: express/libMNN_Express.so
testModelWithDescribe.out: libMNN.so
testModelWithDescribe.out: CMakeFiles/testModelWithDescribe.out.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable testModelWithDescribe.out"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/testModelWithDescribe.out.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/testModelWithDescribe.out.dir/build: testModelWithDescribe.out

.PHONY : CMakeFiles/testModelWithDescribe.out.dir/build

CMakeFiles/testModelWithDescribe.out.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/testModelWithDescribe.out.dir/cmake_clean.cmake
.PHONY : CMakeFiles/testModelWithDescribe.out.dir/clean

CMakeFiles/testModelWithDescribe.out.dir/depend:
	cd /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /root/pan/shunzao_ai_lib-develop/MNN-master /root/pan/shunzao_ai_lib-develop/MNN-master /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/testModelWithDescribe.out.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/testModelWithDescribe.out.dir/depend

