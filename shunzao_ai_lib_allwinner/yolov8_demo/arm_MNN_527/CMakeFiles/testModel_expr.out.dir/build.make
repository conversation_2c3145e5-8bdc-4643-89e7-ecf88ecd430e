# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /root/pan/shunzao_ai_lib-develop/MNN-master

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm

# Include any dependencies generated for this target.
include CMakeFiles/testModel_expr.out.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/testModel_expr.out.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/testModel_expr.out.dir/flags.make

CMakeFiles/testModel_expr.out.dir/tools/cpp/testModel_expr.cpp.o: CMakeFiles/testModel_expr.out.dir/flags.make
CMakeFiles/testModel_expr.out.dir/tools/cpp/testModel_expr.cpp.o: ../tools/cpp/testModel_expr.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/testModel_expr.out.dir/tools/cpp/testModel_expr.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/testModel_expr.out.dir/tools/cpp/testModel_expr.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/testModel_expr.cpp

CMakeFiles/testModel_expr.out.dir/tools/cpp/testModel_expr.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/testModel_expr.out.dir/tools/cpp/testModel_expr.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/testModel_expr.cpp > CMakeFiles/testModel_expr.out.dir/tools/cpp/testModel_expr.cpp.i

CMakeFiles/testModel_expr.out.dir/tools/cpp/testModel_expr.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/testModel_expr.out.dir/tools/cpp/testModel_expr.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/testModel_expr.cpp -o CMakeFiles/testModel_expr.out.dir/tools/cpp/testModel_expr.cpp.s

# Object files for target testModel_expr.out
testModel_expr_out_OBJECTS = \
"CMakeFiles/testModel_expr.out.dir/tools/cpp/testModel_expr.cpp.o"

# External object files for target testModel_expr.out
testModel_expr_out_EXTERNAL_OBJECTS =

testModel_expr.out: CMakeFiles/testModel_expr.out.dir/tools/cpp/testModel_expr.cpp.o
testModel_expr.out: CMakeFiles/testModel_expr.out.dir/build.make
testModel_expr.out: express/libMNN_Express.so
testModel_expr.out: libMNN.so
testModel_expr.out: CMakeFiles/testModel_expr.out.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable testModel_expr.out"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/testModel_expr.out.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/testModel_expr.out.dir/build: testModel_expr.out

.PHONY : CMakeFiles/testModel_expr.out.dir/build

CMakeFiles/testModel_expr.out.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/testModel_expr.out.dir/cmake_clean.cmake
.PHONY : CMakeFiles/testModel_expr.out.dir/clean

CMakeFiles/testModel_expr.out.dir/depend:
	cd /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /root/pan/shunzao_ai_lib-develop/MNN-master /root/pan/shunzao_ai_lib-develop/MNN-master /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/testModel_expr.out.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/testModel_expr.out.dir/depend

