#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

../3rd_party/rapidjson/allocators.h
rapidjson.h
../3rd_party/rapidjson/rapidjson.h

../3rd_party/rapidjson/document.h
reader.h
../3rd_party/rapidjson/reader.h
internal/meta.h
../3rd_party/rapidjson/internal/meta.h
internal/strfunc.h
../3rd_party/rapidjson/internal/strfunc.h
memorystream.h
../3rd_party/rapidjson/memorystream.h
encodedstream.h
../3rd_party/rapidjson/encodedstream.h
new
-
limits
-
iterator
-
utility
-

../3rd_party/rapidjson/encodedstream.h
stream.h
../3rd_party/rapidjson/stream.h
memorystream.h
../3rd_party/rapidjson/memorystream.h

../3rd_party/rapidjson/encodings.h
rapidjson.h
../3rd_party/rapidjson/rapidjson.h

../3rd_party/rapidjson/error/error.h
../rapidjson.h
../3rd_party/rapidjson/rapidjson.h

../3rd_party/rapidjson/internal/biginteger.h
../rapidjson.h
../3rd_party/rapidjson/rapidjson.h
intrin.h
-

../3rd_party/rapidjson/internal/diyfp.h
../rapidjson.h
../3rd_party/rapidjson/rapidjson.h
limits
-
intrin.h
-

../3rd_party/rapidjson/internal/dtoa.h
itoa.h
../3rd_party/rapidjson/internal/itoa.h
diyfp.h
../3rd_party/rapidjson/internal/diyfp.h
ieee754.h
../3rd_party/rapidjson/internal/ieee754.h

../3rd_party/rapidjson/internal/ieee754.h
../rapidjson.h
../3rd_party/rapidjson/rapidjson.h

../3rd_party/rapidjson/internal/itoa.h
../rapidjson.h
../3rd_party/rapidjson/rapidjson.h

../3rd_party/rapidjson/internal/meta.h
../rapidjson.h
../3rd_party/rapidjson/rapidjson.h
type_traits
-

../3rd_party/rapidjson/internal/pow10.h
../rapidjson.h
../3rd_party/rapidjson/rapidjson.h

../3rd_party/rapidjson/internal/stack.h
../allocators.h
../3rd_party/rapidjson/allocators.h
swap.h
../3rd_party/rapidjson/internal/swap.h
cstddef
-

../3rd_party/rapidjson/internal/strfunc.h
../stream.h
../3rd_party/rapidjson/stream.h
cwchar
-

../3rd_party/rapidjson/internal/strtod.h
ieee754.h
../3rd_party/rapidjson/internal/ieee754.h
biginteger.h
../3rd_party/rapidjson/internal/biginteger.h
diyfp.h
../3rd_party/rapidjson/internal/diyfp.h
pow10.h
../3rd_party/rapidjson/internal/pow10.h
climits
-
limits
-

../3rd_party/rapidjson/internal/swap.h
../rapidjson.h
../3rd_party/rapidjson/rapidjson.h

../3rd_party/rapidjson/memorystream.h
stream.h
../3rd_party/rapidjson/stream.h

../3rd_party/rapidjson/msinttypes/inttypes.h
stdint.h
../3rd_party/rapidjson/msinttypes/stdint.h
inttypes.h
-

../3rd_party/rapidjson/msinttypes/stdint.h
stdint.h
-
limits.h
-
wchar.h
-

../3rd_party/rapidjson/prettywriter.h
writer.h
../3rd_party/rapidjson/writer.h

../3rd_party/rapidjson/rapidjson.h
cstdlib
-
cstring
-
rapidjson/...
../3rd_party/rapidjson/rapidjson/...
string
-
msinttypes/stdint.h
../3rd_party/rapidjson/msinttypes/stdint.h
msinttypes/inttypes.h
../3rd_party/rapidjson/msinttypes/inttypes.h
stdint.h
-
inttypes.h
-
endian.h
-
rapidjson/...
../3rd_party/rapidjson/rapidjson/...
cassert
-

../3rd_party/rapidjson/reader.h
allocators.h
../3rd_party/rapidjson/allocators.h
stream.h
../3rd_party/rapidjson/stream.h
encodedstream.h
../3rd_party/rapidjson/encodedstream.h
internal/meta.h
../3rd_party/rapidjson/internal/meta.h
internal/stack.h
../3rd_party/rapidjson/internal/stack.h
internal/strtod.h
../3rd_party/rapidjson/internal/strtod.h
limits
-
intrin.h
-
nmmintrin.h
-
emmintrin.h
-
arm_neon.h
-
stdexcept
-
rapidjson/error/error.h
../3rd_party/rapidjson/rapidjson/error/error.h
rapidjson/reader.h
../3rd_party/rapidjson/rapidjson/reader.h
error/error.h
../3rd_party/rapidjson/error/error.h

../3rd_party/rapidjson/stream.h
rapidjson.h
../3rd_party/rapidjson/rapidjson.h
encodings.h
../3rd_party/rapidjson/encodings.h

../3rd_party/rapidjson/stringbuffer.h
stream.h
../3rd_party/rapidjson/stream.h
internal/stack.h
../3rd_party/rapidjson/internal/stack.h
utility
-
internal/stack.h
../3rd_party/rapidjson/internal/stack.h

../3rd_party/rapidjson/writer.h
stream.h
../3rd_party/rapidjson/stream.h
internal/meta.h
../3rd_party/rapidjson/internal/meta.h
internal/stack.h
../3rd_party/rapidjson/internal/stack.h
internal/strfunc.h
../3rd_party/rapidjson/internal/strfunc.h
internal/dtoa.h
../3rd_party/rapidjson/internal/dtoa.h
internal/itoa.h
../3rd_party/rapidjson/internal/itoa.h
stringbuffer.h
../3rd_party/rapidjson/stringbuffer.h
new
-
intrin.h
-
nmmintrin.h
-
emmintrin.h
-
arm_neon.h
-

../include/MNN/AutoTime.hpp
stdint.h
-
stdio.h
-
MNN/MNNDefine.h
-

../include/MNN/ErrorCode.hpp

../include/MNN/HalideRuntime.h
stddef.h
-
stdint.h
-
stdbool.h
-

../include/MNN/Interpreter.hpp
functional
-
map
-
memory
-
string
-
MNN/ErrorCode.hpp
-
MNN/MNNForwardType.h
-
MNN/Tensor.hpp
-

../include/MNN/MNNDefine.h
assert.h
-
stdio.h
-
TargetConditionals.h
-
hilog/log.h
-
android/log.h
-
syslog.h
-

../include/MNN/MNNForwardType.h
stdint.h
-
stddef.h
-

../include/MNN/Tensor.hpp
vector
-
MNN/HalideRuntime.h
-
MNN/MNNDefine.h
-

../include/MNN/expr/Expr.hpp
functional
-
string
-
vector
-
map
-
memory
-
MNN/HalideRuntime.h
-
MNN/MNNDefine.h
-

/root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/testTrain.cpp
MNN/MNNDefine.h
-
math.h
-
stdio.h
-
stdlib.h
-
string.h
-
MNN/AutoTime.hpp
-
MNN/Interpreter.hpp
-
MNN/Tensor.hpp
-
MNN/expr/Expr.hpp
-
fstream
-
map
-
cmath
-
iostream
-
sstream
-
string
-
set
-
algorithm
-
rapidjson/document.h
/root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/rapidjson/document.h
rapidjson/stringbuffer.h
/root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/rapidjson/stringbuffer.h
rapidjson/prettywriter.h
/root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/rapidjson/prettywriter.h

