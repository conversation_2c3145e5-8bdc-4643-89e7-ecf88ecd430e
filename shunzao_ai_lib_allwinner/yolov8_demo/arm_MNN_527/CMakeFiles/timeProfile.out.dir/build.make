# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /root/pan/shunzao_ai_lib-develop/MNN-master

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm

# Include any dependencies generated for this target.
include CMakeFiles/timeProfile.out.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/timeProfile.out.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/timeProfile.out.dir/flags.make

CMakeFiles/timeProfile.out.dir/tools/cpp/timeProfile.cpp.o: CMakeFiles/timeProfile.out.dir/flags.make
CMakeFiles/timeProfile.out.dir/tools/cpp/timeProfile.cpp.o: ../tools/cpp/timeProfile.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/timeProfile.out.dir/tools/cpp/timeProfile.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/timeProfile.out.dir/tools/cpp/timeProfile.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/timeProfile.cpp

CMakeFiles/timeProfile.out.dir/tools/cpp/timeProfile.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/timeProfile.out.dir/tools/cpp/timeProfile.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/timeProfile.cpp > CMakeFiles/timeProfile.out.dir/tools/cpp/timeProfile.cpp.i

CMakeFiles/timeProfile.out.dir/tools/cpp/timeProfile.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/timeProfile.out.dir/tools/cpp/timeProfile.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/timeProfile.cpp -o CMakeFiles/timeProfile.out.dir/tools/cpp/timeProfile.cpp.s

CMakeFiles/timeProfile.out.dir/tools/cpp/revertMNNModel.cpp.o: CMakeFiles/timeProfile.out.dir/flags.make
CMakeFiles/timeProfile.out.dir/tools/cpp/revertMNNModel.cpp.o: ../tools/cpp/revertMNNModel.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/timeProfile.out.dir/tools/cpp/revertMNNModel.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/timeProfile.out.dir/tools/cpp/revertMNNModel.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/revertMNNModel.cpp

CMakeFiles/timeProfile.out.dir/tools/cpp/revertMNNModel.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/timeProfile.out.dir/tools/cpp/revertMNNModel.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/revertMNNModel.cpp > CMakeFiles/timeProfile.out.dir/tools/cpp/revertMNNModel.cpp.i

CMakeFiles/timeProfile.out.dir/tools/cpp/revertMNNModel.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/timeProfile.out.dir/tools/cpp/revertMNNModel.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/revertMNNModel.cpp -o CMakeFiles/timeProfile.out.dir/tools/cpp/revertMNNModel.cpp.s

CMakeFiles/timeProfile.out.dir/tools/cpp/Profiler.cpp.o: CMakeFiles/timeProfile.out.dir/flags.make
CMakeFiles/timeProfile.out.dir/tools/cpp/Profiler.cpp.o: ../tools/cpp/Profiler.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/timeProfile.out.dir/tools/cpp/Profiler.cpp.o"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++  $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/timeProfile.out.dir/tools/cpp/Profiler.cpp.o -c /root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/Profiler.cpp

CMakeFiles/timeProfile.out.dir/tools/cpp/Profiler.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/timeProfile.out.dir/tools/cpp/Profiler.cpp.i"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/Profiler.cpp > CMakeFiles/timeProfile.out.dir/tools/cpp/Profiler.cpp.i

CMakeFiles/timeProfile.out.dir/tools/cpp/Profiler.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/timeProfile.out.dir/tools/cpp/Profiler.cpp.s"
	/root/pan/shunzao_ai_lib-develop/0-toolchains/gcc-arm-10.3-2021.07-x86_64-aarch64-none-linux-gnu/bin/aarch64-none-linux-gnu-g++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/Profiler.cpp -o CMakeFiles/timeProfile.out.dir/tools/cpp/Profiler.cpp.s

# Object files for target timeProfile.out
timeProfile_out_OBJECTS = \
"CMakeFiles/timeProfile.out.dir/tools/cpp/timeProfile.cpp.o" \
"CMakeFiles/timeProfile.out.dir/tools/cpp/revertMNNModel.cpp.o" \
"CMakeFiles/timeProfile.out.dir/tools/cpp/Profiler.cpp.o"

# External object files for target timeProfile.out
timeProfile_out_EXTERNAL_OBJECTS =

timeProfile.out: CMakeFiles/timeProfile.out.dir/tools/cpp/timeProfile.cpp.o
timeProfile.out: CMakeFiles/timeProfile.out.dir/tools/cpp/revertMNNModel.cpp.o
timeProfile.out: CMakeFiles/timeProfile.out.dir/tools/cpp/Profiler.cpp.o
timeProfile.out: CMakeFiles/timeProfile.out.dir/build.make
timeProfile.out: express/libMNN_Express.so
timeProfile.out: libMNN.so
timeProfile.out: CMakeFiles/timeProfile.out.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Linking CXX executable timeProfile.out"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/timeProfile.out.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/timeProfile.out.dir/build: timeProfile.out

.PHONY : CMakeFiles/timeProfile.out.dir/build

CMakeFiles/timeProfile.out.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/timeProfile.out.dir/cmake_clean.cmake
.PHONY : CMakeFiles/timeProfile.out.dir/clean

CMakeFiles/timeProfile.out.dir/depend:
	cd /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /root/pan/shunzao_ai_lib-develop/MNN-master /root/pan/shunzao_ai_lib-develop/MNN-master /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm /root/pan/shunzao_ai_lib-develop/MNN-master/build_arm/CMakeFiles/timeProfile.out.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/timeProfile.out.dir/depend

