#IncludeRegexLine: ^[ 	]*[#%][ 	]*(include|import)[ 	]*[<"]([^">]+)([">])

#IncludeRegexScan: ^.*$

#IncludeRegexComplain: ^$

#IncludeRegexTransform: 

../include/MNN/HalideRuntime.h
stddef.h
-
stdint.h
-
stdbool.h
-

../include/MNN/MNNDefine.h
assert.h
-
stdio.h
-
TargetConditionals.h
-
hilog/log.h
-
android/log.h
-
syslog.h
-

../include/MNN/Tensor.hpp
vector
-
MNN/HalideRuntime.h
-
MNN/MNNDefine.h
-

../source/math/Matrix.hpp
stdio.h
-
memory
-
MNN/Tensor.hpp
-

../source/math/WingoradGenerater.hpp
memory
-
vector
-
math/Matrix.hpp
../source/math/math/Matrix.hpp

/root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/winogradExample.cpp
stdlib.h
-
string.h
-
MNN/MNNDefine.h
-
math/Matrix.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/math/Matrix.hpp
math/WingoradGenerater.hpp
/root/pan/shunzao_ai_lib-develop/MNN-master/tools/cpp/math/WingoradGenerater.hpp

