<!--
    SPDX-FileCopyrightText: Copyright 2024-2025 Arm Limited and/or its affiliates <<EMAIL>>

    SPDX-License-Identifier: Apache-2.0
-->

# KleidiAI documentation and guides

Welcome to the KleidiAI documentation hub. Here, you will find a variety of step-by-step guides to help you master this library. For instance, you can explore introductory tutorials on running a micro-kernel and discover best practices for optimizing the performance of your AI framework on Arm® CPUs.

## Table of Contents

### Guides

- [How to run the int4 matmul micro-kernels](matmul_qsi4cx/README.md)
- [How to run the indirect matmul micro-kernels](imatmul/README.md)
- [KleidiAI kernel overview](../kai/ukernels/matmul/README.md)
- [Packing kernels description](../kai/ukernels/matmul/pack/README.md)
